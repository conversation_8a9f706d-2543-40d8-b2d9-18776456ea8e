#!/usr/bin/env python3
"""Quick test without sklearn to verify basic functionality."""

print("🚀 Quick Instagram Scraper Test...")

try:
    print("1. Testing basic imports...")
    import requests
    import pydantic
    from pydantic import BaseModel
    from datetime import datetime
    import re
    print("   ✅ Basic imports successful")
    
    print("2. Testing contact extraction...")
    def extract_emails(text):
        return re.findall(r'[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}', text)
    
    test_text = "Contact <NAME_EMAIL> for business inquiries"
    emails = extract_emails(test_text)
    print(f"   ✅ Email extraction: Found {emails}")
    
    print("3. Testing Pydantic model...")
    class TestProfile(BaseModel):
        username: str
        full_name: str
        followers: int
        scraped_at: datetime = datetime.utcnow()
    
    profile = TestProfile(
        username="testuser",
        full_name="Test User", 
        followers=1000
    )
    print(f"   ✅ Pydantic model: {profile.username} with {profile.followers} followers")
    
    print("4. Testing Instagram API call structure...")
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "x-ig-app-id": "936619743392459"
    }
    print(f"   ✅ Headers prepared: {len(headers)} headers")
    
    print("\n🎉 Core components are working!")
    print("✅ Ready to proceed with full functionality testing.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
