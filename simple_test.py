#!/usr/bin/env python3
"""Simple test to verify the scraper components work."""

print("🚀 Testing Instagram Scraper Components...")

try:
    print("1. Testing imports...")
    import requests
    import pydantic
    from pydantic import BaseModel
    from datetime import datetime
    import re
    print("   ✅ Basic imports successful")
    
    print("2. Testing sklearn...")
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.naive_bayes import MultinomialNB
    print("   ✅ Sklearn imports successful")
    
    print("3. Testing industry classifier...")
    # Simple industry classifier test
    industry_data = {
        "coach": ["life coach", "executive coach", "career coaching"],
        "trainer": ["fitness trainer", "personal trainer", "yoga instructor"],
        "speaker": ["motivational speaker", "keynote speaker", "public speaking"]
    }
    
    documents = []
    labels = []
    for industry, keywords in industry_data.items():
        for keyword in keywords:
            documents.append(keyword)
            labels.append(industry)
    
    vectorizer = TfidfVectorizer(stop_words='english')
    model = MultinomialNB()
    
    X = vectorizer.fit_transform(documents)
    model.fit(X, labels)
    
    # Test prediction
    test_bio = "I am a life coach helping entrepreneurs"
    features = vectorizer.transform([test_bio])
    prediction = model.predict(features)[0]
    print(f"   ✅ Industry prediction: '{test_bio}' -> {prediction}")
    
    print("4. Testing contact extraction...")
    def extract_emails(text):
        return re.findall(r'[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}', text)
    
    test_text = "Contact <NAME_EMAIL> for business inquiries"
    emails = extract_emails(test_text)
    print(f"   ✅ Email extraction: Found {emails}")
    
    print("5. Testing Pydantic model...")
    class TestProfile(BaseModel):
        username: str
        full_name: str
        followers: int
        scraped_at: datetime = datetime.utcnow()
    
    profile = TestProfile(
        username="testuser",
        full_name="Test User", 
        followers=1000
    )
    print(f"   ✅ Pydantic model: {profile.username} with {profile.followers} followers")
    
    print("\n🎉 All core components are working correctly!")
    print("✅ The Instagram scraper is ready for configuration and use.")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
