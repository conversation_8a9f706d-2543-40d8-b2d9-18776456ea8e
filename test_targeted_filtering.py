#!/usr/bin/env python3
"""
Test the targeted filtering functionality for the Instagram scraper.
Tests the new industry classification and filtering criteria.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from instagram_scraper import (
    IndustryClassifier,
    is_us_based_profile,
    has_recent_posts,
    meets_follower_criteria,
    is_target_industry
)
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_industry_classification():
    """Test the updated industry classification for target industries."""
    print("\n=== Testing Target Industry Classification ===")
    
    try:
        classifier = IndustryClassifier()
        
        test_cases = [
            # Keynote Speakers
            ("Professional keynote speaker and leadership expert", "keynote_speaker"),
            ("Motivational speaker for corporate events", "keynote_speaker"),
            ("TEDx speaker and workshop facilitator", "keynote_speaker"),
            
            # Coaches/Consultants/Course Creators
            ("Life coach helping entrepreneurs build successful businesses", "coach_consultant_creator"),
            ("Business consultant and online course creator", "coach_consultant_creator"),
            ("Marketing strategist with coaching programs", "coach_consultant_creator"),
            ("Executive coach and mastermind leader", "coach_consultant_creator"),
            
            # Authors
            ("Bestselling author of business books", "author"),
            ("Self-help author and thought leader", "author"),
            ("Published author and writing coach", "author"),
            ("Amazon bestselling author", "author"),
            
            # Non-target (should not match)
            ("Fitness trainer and gym owner", "other"),
            ("Restaurant chef and food blogger", "other"),
            ("Travel photographer", "other")
        ]
        
        correct_predictions = 0
        for bio, expected_category in test_cases:
            prediction = classifier.predict(bio)
            is_target = prediction in ['keynote_speaker', 'coach_consultant_creator', 'author']
            expected_is_target = expected_category != "other"
            
            if is_target == expected_is_target:
                correct_predictions += 1
                status = "✅"
            else:
                status = "❌"
            
            print(f"   {status} '{bio[:50]}...' -> {prediction} (target: {is_target})")
        
        accuracy = correct_predictions / len(test_cases)
        print(f"\n   Accuracy: {accuracy:.1%} ({correct_predictions}/{len(test_cases)})")
        
        if accuracy >= 0.8:
            print("✅ Industry classification working well")
            return True
        else:
            print("⚠️ Industry classification needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ Industry classification failed: {e}")
        return False

def test_us_location_detection():
    """Test US location detection."""
    print("\n=== Testing US Location Detection ===")
    
    try:
        test_profiles = [
            # Should be detected as US
            {"biography": "Business coach based in California", "external_url": ""}, 
            {"biography": "Speaker from New York City", "external_url": ""},
            {"biography": "Author living in USA", "external_url": ""},
            {"biography": "Coach in Los Angeles helping entrepreneurs", "external_url": ""},
            {"biography": "Consultant", "external_url": "https://mysite.us"},
            
            # Should NOT be detected as US
            {"biography": "Coach from London, UK", "external_url": ""},
            {"biography": "Speaker based in Toronto, Canada", "external_url": ""},
            {"biography": "Author from Sydney, Australia", "external_url": ""},
            {"biography": "Business consultant", "external_url": "https://mysite.co.uk"},
            {"biography": "No location mentioned", "external_url": ""}
        ]
        
        expected_results = [True, True, True, True, True, False, False, False, False, False]
        
        correct = 0
        for i, profile in enumerate(test_profiles):
            result = is_us_based_profile(profile)
            expected = expected_results[i]
            
            if result == expected:
                correct += 1
                status = "✅"
            else:
                status = "❌"
            
            bio_short = profile["biography"][:40] + "..." if len(profile["biography"]) > 40 else profile["biography"]
            print(f"   {status} '{bio_short}' -> US: {result}")
        
        accuracy = correct / len(test_profiles)
        print(f"\n   Accuracy: {accuracy:.1%} ({correct}/{len(test_profiles)})")
        
        if accuracy >= 0.8:
            print("✅ US location detection working well")
            return True
        else:
            print("⚠️ US location detection needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ US location detection failed: {e}")
        return False

def test_follower_criteria():
    """Test follower count filtering."""
    print("\n=== Testing Follower Criteria ===")
    
    try:
        test_cases = [
            # Should pass (100-10k followers)
            ({"edge_followed_by": {"count": 500}}, True),
            ({"edge_followed_by": {"count": 2500}}, True),
            ({"edge_followed_by": {"count": 9999}}, True),
            ({"edge_followed_by": {"count": 100}}, True),
            ({"edge_followed_by": {"count": 10000}}, True),
            
            # Should fail
            ({"edge_followed_by": {"count": 50}}, False),  # Too few
            ({"edge_followed_by": {"count": 15000}}, False),  # Too many
            ({"edge_followed_by": {"count": 0}}, False),  # No followers
            ({}, False),  # No data
        ]
        
        correct = 0
        for profile_data, expected in test_cases:
            result = meets_follower_criteria(profile_data)
            
            if result == expected:
                correct += 1
                status = "✅"
            else:
                status = "❌"
            
            followers = profile_data.get("edge_followed_by", {}).get("count", "N/A")
            print(f"   {status} {followers} followers -> Pass: {result}")
        
        accuracy = correct / len(test_cases)
        print(f"\n   Accuracy: {accuracy:.1%} ({correct}/{len(test_cases)})")
        
        if accuracy == 1.0:
            print("✅ Follower criteria working perfectly")
            return True
        else:
            print("⚠️ Follower criteria has issues")
            return False
            
    except Exception as e:
        print(f"❌ Follower criteria test failed: {e}")
        return False

def test_recent_posts():
    """Test recent posting activity detection."""
    print("\n=== Testing Recent Posts Detection ===")
    
    try:
        test_cases = [
            # Should pass (has posts)
            ({"edge_owner_to_timeline_media": {"count": 50}}, True),
            ({"edge_owner_to_timeline_media": {"count": 1}}, True),
            ({"edge_owner_to_timeline_media": {"count": 500}}, True),
            
            # Should fail (no posts)
            ({"edge_owner_to_timeline_media": {"count": 0}}, False),
            ({}, False),  # No data
        ]
        
        correct = 0
        for profile_data, expected in test_cases:
            result = has_recent_posts(profile_data)
            
            if result == expected:
                correct += 1
                status = "✅"
            else:
                status = "❌"
            
            posts = profile_data.get("edge_owner_to_timeline_media", {}).get("count", "N/A")
            print(f"   {status} {posts} posts -> Recent activity: {result}")
        
        accuracy = correct / len(test_cases)
        print(f"\n   Accuracy: {accuracy:.1%} ({correct}/{len(test_cases)})")
        
        if accuracy == 1.0:
            print("✅ Recent posts detection working perfectly")
            return True
        else:
            print("⚠️ Recent posts detection has issues")
            return False
            
    except Exception as e:
        print(f"❌ Recent posts test failed: {e}")
        return False

def test_complete_filtering():
    """Test complete filtering pipeline."""
    print("\n=== Testing Complete Filtering Pipeline ===")
    
    try:
        classifier = IndustryClassifier()
        
        # Mock profile that should pass all filters
        good_profile = {
            "biography": "Life coach based in California helping entrepreneurs build successful businesses. Contact: <EMAIL>",
            "external_url": "https://mycoaching.com",
            "edge_followed_by": {"count": 2500},
            "edge_owner_to_timeline_media": {"count": 150},
            "full_name": "Jane Smith",
            "is_verified": False
        }
        
        # Test each filter
        bio = good_profile.get("biography", "")
        
        follower_check = meets_follower_criteria(good_profile)
        posts_check = has_recent_posts(good_profile)
        us_check = is_us_based_profile(good_profile)
        industry_check = is_target_industry(bio, classifier)
        
        print(f"   Follower criteria (100-10k): {follower_check}")
        print(f"   Recent posts: {posts_check}")
        print(f"   US-based: {us_check}")
        print(f"   Target industry: {industry_check}")
        
        all_pass = all([follower_check, posts_check, us_check, industry_check])
        
        if all_pass:
            print("✅ Complete filtering pipeline working - profile would be processed")
            return True
        else:
            print("❌ Complete filtering pipeline failed - profile would be skipped")
            return False
            
    except Exception as e:
        print(f"❌ Complete filtering test failed: {e}")
        return False

def main():
    """Run all targeted filtering tests."""
    print("🎯 Testing Instagram Scraper Targeted Filtering")
    print("=" * 60)
    
    tests = [
        test_industry_classification,
        test_us_location_detection,
        test_follower_criteria,
        test_recent_posts,
        test_complete_filtering
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Targeted Filtering Test Results:")
    print(f"✅ Passed: {sum(results)}/{len(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}/{len(results)}")
    
    if sum(results) >= len(results) - 1:  # Allow 1 failure
        print("\n🎯 Targeted filtering is working! Ready to find:")
        print("   • Keynote Speakers")
        print("   • Online Coaches/Consultants/Course Creators")
        print("   • Authors")
        print("   • With < 10k followers")
        print("   • US-based with recent activity")
        return True
    else:
        print("\n⚠️ Some filtering components need attention.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
