#!/usr/bin/env python3
"""
Instaloader Demo - Real Instagram Profile Scraping
This demonstrates how to use Instaloader to get real Instagram profile data.
"""

import sys
import os
import json
import time
import csv
from datetime import datetime
from typing import List, Dict, Optional

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    import instaloader
    INSTALOADER_AVAILABLE = True
    logger.info("✅ Instaloader is available for real Instagram scraping")
except ImportError:
    INSTALOADER_AVAILABLE = False
    logger.warning("❌ Instaloader not available. Install with: pip install instaloader")

def test_instaloader_basic():
    """Test basic Instaloader functionality with public profiles."""
    if not INSTALOADER_AVAILABLE:
        print("❌ Instaloader not installed!")
        print("Install with: pip install instaloader")
        return False
    
    print("🚀 Testing Instaloader with Real Instagram Profiles")
    print("=" * 60)
    
    # Initialize Instaloader
    loader = instaloader.Instaloader(
        download_pictures=False,
        download_videos=False,
        download_video_thumbnails=False,
        download_geotags=False,
        download_comments=False,
        save_metadata=False,
        compress_json=False
    )
    
    # Test with some well-known public profiles
    test_usernames = [
        "instagram",      # Instagram's official account
        "natgeo",        # National Geographic
        "nasa",          # NASA
        "therock",       # Dwayne Johnson
        "cristiano"      # Cristiano Ronaldo
    ]
    
    print(f"🔍 Testing with {len(test_usernames)} public profiles...")
    print("Note: These are large accounts for testing - your target would be smaller accounts")
    print()
    
    successful_profiles = []
    
    for i, username in enumerate(test_usernames, 1):
        print(f"[{i}/{len(test_usernames)}] Testing @{username}...")
        
        try:
            # Get profile
            profile = instaloader.Profile.from_username(loader.context, username)
            
            # Extract basic data
            profile_data = {
                'username': profile.username,
                'full_name': profile.full_name,
                'biography': profile.biography,
                'followers': profile.followers,
                'following': profile.followees,
                'posts_count': profile.mediacount,
                'is_verified': profile.is_verified,
                'is_private': profile.is_private,
                'external_url': profile.external_url,
                'is_business_account': profile.is_business_account,
                'scraped_at': datetime.now().isoformat()
            }
            
            print(f"   ✅ Success!")
            print(f"   👤 {profile_data['full_name']}")
            print(f"   👥 {profile_data['followers']:,} followers")
            print(f"   📸 {profile_data['posts_count']} posts")
            print(f"   ✓ Verified: {profile_data['is_verified']}")
            print(f"   🔒 Private: {profile_data['is_private']}")
            print(f"   📝 Bio: {profile_data['biography'][:50]}..." if profile_data['biography'] else "   📝 Bio: (empty)")
            
            successful_profiles.append(profile_data)
            
        except instaloader.exceptions.ProfileNotExistsException:
            print(f"   ❌ Profile @{username} does not exist")
        except instaloader.exceptions.LoginRequiredException:
            print(f"   ⚠️ Login required to access @{username}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()
        
        # Rate limiting
        if i < len(test_usernames):
            print("   ⏳ Waiting 3 seconds...")
            time.sleep(3)
    
    print("=" * 60)
    print(f"📊 RESULTS:")
    print(f"✅ Successfully scraped: {len(successful_profiles)}/{len(test_usernames)} profiles")
    
    if successful_profiles:
        # Save results
        with open('instaloader_test_results.json', 'w') as f:
            json.dump(successful_profiles, f, indent=2)
        print(f"💾 Results saved to: instaloader_test_results.json")
        
        print(f"\n🎯 WHAT THIS MEANS FOR YOUR LEAD GENERATION:")
        print("✅ Instaloader can fetch real Instagram profile data")
        print("✅ You can get follower counts, bios, verification status")
        print("✅ Works with public profiles without login")
        print("✅ Perfect for your lead qualification system")
        
        print(f"\n💡 NEXT STEPS:")
        print("1. Use your discovered usernames from the simulation")
        print("2. Replace test usernames with your target profiles")
        print("3. Apply your filtering criteria to real data")
        print("4. Generate qualified leads from real Instagram profiles")
        
        return True
    else:
        print("❌ No profiles could be scraped")
        print("This might be due to:")
        print("- Network connectivity issues")
        print("- Instagram rate limiting")
        print("- Need for login credentials")
        return False

def demo_with_target_usernames():
    """Demo with usernames that might match your target criteria."""
    if not INSTALOADER_AVAILABLE:
        return False
    
    print("\n" + "=" * 60)
    print("🎯 DEMO: Testing with Target-Like Usernames")
    print("=" * 60)
    print("Note: These are example usernames - replace with real discovered usernames")
    
    # Initialize Instaloader
    loader = instaloader.Instaloader(
        download_pictures=False,
        download_videos=False,
        download_video_thumbnails=False,
        download_geotags=False,
        download_comments=False,
        save_metadata=False,
        compress_json=False
    )
    
    # Example usernames that might exist and match your criteria
    # (These are examples - you'd use your discovered usernames)
    target_usernames = [
        "lifecoach",
        "businesscoach", 
        "motivational",
        "entrepreneur",
        "author"
    ]
    
    print(f"🔍 Testing with {len(target_usernames)} target-like usernames...")
    print("(These are generic examples - use your discovered usernames for real results)")
    print()
    
    qualified_profiles = []
    
    for i, username in enumerate(target_usernames, 1):
        print(f"[{i}/{len(target_usernames)}] Testing @{username}...")
        
        try:
            profile = instaloader.Profile.from_username(loader.context, username)
            
            # Check if it might qualify (basic check)
            followers = profile.followers
            is_public = not profile.is_private
            has_bio = bool(profile.biography)
            
            print(f"   ✅ Profile exists!")
            print(f"   👥 {followers:,} followers")
            print(f"   🔒 Public: {is_public}")
            print(f"   📝 Has bio: {has_bio}")
            
            # Simple qualification check
            might_qualify = (
                100 <= followers <= 10000 and  # Follower range
                is_public and                   # Public profile
                has_bio                        # Has biography
            )
            
            if might_qualify:
                print(f"   🎉 MIGHT QUALIFY for your criteria!")
                qualified_profiles.append({
                    'username': username,
                    'followers': followers,
                    'biography': profile.biography,
                    'is_verified': profile.is_verified
                })
            else:
                reasons = []
                if not (100 <= followers <= 10000):
                    reasons.append(f"followers ({followers:,})")
                if not is_public:
                    reasons.append("private")
                if not has_bio:
                    reasons.append("no bio")
                print(f"   ❌ Filtered out: {', '.join(reasons)}")
            
        except instaloader.exceptions.ProfileNotExistsException:
            print(f"   ❌ Profile @{username} does not exist")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()
        
        # Rate limiting
        if i < len(target_usernames):
            time.sleep(2)
    
    print(f"📊 QUALIFICATION RESULTS:")
    print(f"✅ Profiles that might qualify: {len(qualified_profiles)}")
    
    if qualified_profiles:
        print(f"\n🎯 POTENTIALLY QUALIFIED PROFILES:")
        for profile in qualified_profiles:
            print(f"• @{profile['username']} - {profile['followers']:,} followers")
            print(f"  Bio: {profile['biography'][:50]}..." if profile['biography'] else "  Bio: (empty)")
    
    return len(qualified_profiles) > 0

def show_usage_guide():
    """Show how to use Instaloader for your lead generation."""
    print("\n" + "=" * 60)
    print("📋 HOW TO USE INSTALOADER FOR LEAD GENERATION")
    print("=" * 60)
    
    print("🔧 SETUP:")
    print("1. pip install instaloader")
    print("2. Use your discovered usernames from the simulation")
    print("3. Apply your filtering criteria to real data")
    
    print("\n🎯 BASIC USAGE:")
    print("""
import instaloader

# Initialize
loader = instaloader.Instaloader()

# Get profile
profile = instaloader.Profile.from_username(loader.context, "username")

# Extract data
data = {
    'username': profile.username,
    'followers': profile.followers,
    'biography': profile.biography,
    'is_verified': profile.is_verified,
    'external_url': profile.external_url
}
""")
    
    print("🚀 INTEGRATION WITH YOUR SYSTEM:")
    print("1. Replace simulated usernames with real ones")
    print("2. Use Instaloader to fetch real profile data")
    print("3. Apply your industry classification")
    print("4. Filter by follower count, location, etc.")
    print("5. Extract contact information")
    print("6. Save qualified leads to Excel")
    
    print("\n⚠️ IMPORTANT NOTES:")
    print("• Instagram may require login for some operations")
    print("• Respect rate limits (add delays between requests)")
    print("• Some profiles may be private or require authentication")
    print("• Always comply with Instagram's Terms of Service")
    
    print("\n💡 FOR PRODUCTION USE:")
    print("• Consider using Instagram login for better access")
    print("• Implement proper error handling and retries")
    print("• Add proxy rotation for large-scale scraping")
    print("• Monitor for rate limiting and adjust delays")

def main():
    """Main function to run Instaloader demo."""
    print("🚀 Instaloader Demo - Real Instagram Profile Scraping")
    print("=" * 60)
    
    if not INSTALOADER_AVAILABLE:
        print("❌ Instaloader not installed!")
        print("Install with: pip install instaloader")
        return False
    
    # Test basic functionality
    basic_success = test_instaloader_basic()
    
    if basic_success:
        # Demo with target-like usernames
        target_success = demo_with_target_usernames()
        
        # Show usage guide
        show_usage_guide()
        
        print("\n🎉 INSTALOADER DEMO COMPLETE!")
        print("✅ You now know how to use Instaloader for real Instagram scraping")
        print("🎯 Ready to integrate with your lead generation system!")
        
        return True
    else:
        print("\n❌ Basic test failed. Check your internet connection and try again.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
