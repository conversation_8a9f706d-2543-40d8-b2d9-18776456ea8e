#!/usr/bin/env python3
"""
Demo script showing how the Instagram scraper works with Excel storage.
This demonstrates the complete workflow for targeted lead generation.
"""

import sys
import os
import json
from datetime import datetime

# Mock the Excel functionality for demonstration
class MockExcelHandler:
    """Mock Excel handler to demonstrate functionality without pandas dependency."""
    
    def __init__(self, file_path="instagram_leads_targeted.xlsx"):
        self.file_path = file_path
        self.data = []
        print(f"📊 Excel Handler initialized: {file_path}")
        print("   (In real usage, this creates an Excel file with proper headers)")
    
    def append_profile(self, profile_data):
        """Add a profile to the mock Excel storage."""
        self.data.append(profile_data)
        print(f"   ✅ Added {profile_data['username']} to Excel file")
        print(f"      Industry: {profile_data['industry']}")
        print(f"      Followers: {profile_data['followers']}")
        print(f"      Email: {profile_data.get('verified_email', 'N/A')}")
    
    def get_stats(self):
        """Get statistics about stored data."""
        if not self.data:
            return {"total_profiles": 0}
        
        industries = {}
        emails_count = 0
        total_followers = 0
        
        for profile in self.data:
            industry = profile.get('industry', 'unknown')
            industries[industry] = industries.get(industry, 0) + 1
            if profile.get('verified_email'):
                emails_count += 1
            total_followers += profile.get('followers', 0)
        
        return {
            "total_profiles": len(self.data),
            "industries": industries,
            "profiles_with_email": emails_count,
            "avg_followers": total_followers / len(self.data) if self.data else 0
        }
    
    def save_to_json(self, filename="demo_results.json"):
        """Save demo results to JSON for inspection."""
        with open(filename, 'w') as f:
            json.dump(self.data, f, indent=2, default=str)
        print(f"📁 Demo results saved to: {filename}")

def demo_profile_filtering():
    """Demonstrate the profile filtering logic."""
    print("\n🎯 DEMO: Profile Filtering for Target Industries")
    print("=" * 60)
    
    # Mock profiles representing different scenarios
    test_profiles = [
        {
            "username": "lifecoach_sarah",
            "full_name": "Sarah Johnson",
            "biography": "Life coach helping entrepreneurs build successful businesses. Based in California. Contact: <EMAIL>",
            "edge_followed_by": {"count": 2500},
            "edge_owner_to_timeline_media": {"count": 150},
            "external_url": "https://sarahcoaching.com",
            "is_verified": False
        },
        {
            "username": "speaker_mike",
            "full_name": "Mike Thompson",
            "biography": "Keynote speaker and author from New York. Inspiring teams worldwide.",
            "edge_followed_by": {"count": 8500},
            "edge_owner_to_timeline_media": {"count": 200},
            "external_url": "https://mikespeaks.com",
            "is_verified": False
        },
        {
            "username": "author_jane",
            "full_name": "Jane Smith",
            "biography": "Bestselling author of business books. Writing coach based in Texas. <EMAIL>",
            "edge_followed_by": {"count": 4200},
            "edge_owner_to_timeline_media": {"count": 80},
            "external_url": "https://janeauthor.com",
            "is_verified": False
        },
        {
            "username": "fitness_trainer_uk",
            "full_name": "Tom Wilson",
            "biography": "Personal trainer from London, UK. Fitness enthusiast.",
            "edge_followed_by": {"count": 3000},
            "edge_owner_to_timeline_media": {"count": 300},
            "external_url": "https://tomfitness.co.uk",
            "is_verified": False
        },
        {
            "username": "mega_influencer",
            "full_name": "Celebrity Coach",
            "biography": "Life coach and motivational speaker from Los Angeles",
            "edge_followed_by": {"count": 50000},  # Too many followers
            "edge_owner_to_timeline_media": {"count": 500},
            "external_url": "https://megacoach.com",
            "is_verified": True
        }
    ]
    
    # Import filtering functions
    try:
        from instagram_scraper import (
            IndustryClassifier,
            meets_follower_criteria,
            has_recent_posts,
            is_us_based_profile,
            is_target_industry,
            extract_contact_info
        )
        
        classifier = IndustryClassifier()
        excel_handler = MockExcelHandler()
        
        print("Processing profiles...")
        qualified_count = 0
        
        for profile in test_profiles:
            username = profile["username"]
            bio = profile.get("biography", "")
            
            print(f"\n📋 Analyzing: @{username}")
            print(f"   Bio: {bio[:50]}...")
            
            # Apply filters
            follower_check = meets_follower_criteria(profile)
            posts_check = has_recent_posts(profile)
            us_check = is_us_based_profile(profile)
            industry_check = is_target_industry(bio, classifier)
            
            print(f"   ✓ Followers (100-10k): {follower_check}")
            print(f"   ✓ Recent posts: {posts_check}")
            print(f"   ✓ US-based: {us_check}")
            print(f"   ✓ Target industry: {industry_check}")
            
            # Check if profile qualifies
            if all([follower_check, posts_check, us_check, industry_check]):
                print(f"   🎉 QUALIFIED - Adding to Excel!")
                qualified_count += 1
                
                # Extract contact info
                contact_info = extract_contact_info(profile)
                
                # Create profile data for Excel
                profile_data = {
                    "username": username,
                    "full_name": profile.get("full_name", ""),
                    "first_name": profile.get("full_name", "").split()[0] if profile.get("full_name") else "",
                    "last_name": " ".join(profile.get("full_name", "").split()[1:]) if profile.get("full_name") else "",
                    "followers": profile.get("edge_followed_by", {}).get("count", 0),
                    "following": profile.get("edge_follow", {}).get("count", 0),
                    "posts_count": profile.get("edge_owner_to_timeline_media", {}).get("count", 0),
                    "verified_email": contact_info['emails'][0] if contact_info['emails'] else None,
                    "phone": contact_info['phones'][0] if contact_info['phones'] else None,
                    "industry": classifier.predict(bio),
                    "profile_url": f"https://instagram.com/{username}",
                    "is_verified": profile.get("is_verified", False),
                    "scraped_at": datetime.now().isoformat()
                }
                
                excel_handler.append_profile(profile_data)
            else:
                print(f"   ❌ FILTERED OUT")
        
        # Show final statistics
        print(f"\n📊 FILTERING RESULTS")
        print("=" * 40)
        print(f"Total profiles analyzed: {len(test_profiles)}")
        print(f"Qualified profiles: {qualified_count}")
        print(f"Filter success rate: {qualified_count/len(test_profiles)*100:.1f}%")
        
        if qualified_count > 0:
            stats = excel_handler.get_stats()
            print(f"\n📈 QUALIFIED LEADS SUMMARY:")
            print(f"   Total leads: {stats['total_profiles']}")
            print(f"   Industries: {stats['industries']}")
            print(f"   With email: {stats['profiles_with_email']}")
            print(f"   Avg followers: {stats['avg_followers']:.0f}")
            
            # Save demo results
            excel_handler.save_to_json()
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import filtering functions: {e}")
        return False

def show_excel_setup_guide():
    """Show how to set up Excel functionality."""
    print("\n📋 EXCEL SETUP GUIDE")
    print("=" * 50)
    print()
    print("1. Install Excel dependencies:")
    print("   pip install pandas openpyxl")
    print()
    print("2. The scraper will automatically:")
    print("   ✅ Create instagram_leads_targeted.xlsx")
    print("   ✅ Add proper column headers")
    print("   ✅ Save qualified profiles automatically")
    print("   ✅ Handle data validation and formatting")
    print()
    print("3. Excel file will contain:")
    print("   • Username & Full Name")
    print("   • Follower/Following counts")
    print("   • Contact info (email/phone)")
    print("   • Industry classification")
    print("   • Profile URL & verification status")
    print("   • Timestamp of data collection")
    print()
    print("4. To use with real Instagram data:")
    print("   • Add target usernames to instagram_scraper.py")
    print("   • Run: python instagram_scraper.py")
    print("   • Check results in the Excel file")
    print()
    print("🎯 Perfect for lead generation workflows!")

def main():
    """Run the Excel functionality demo."""
    print("🚀 Instagram Lead Generation Scraper - Excel Demo")
    print("=" * 60)
    print()
    print("This demo shows how the scraper filters and stores leads in Excel.")
    print("Target: Keynote Speakers, Coaches/Consultants/Course Creators, Authors")
    print("Criteria: 100-10k followers, US-based, recent activity")
    
    # Run the demo
    success = demo_profile_filtering()
    
    if success:
        print("\n🎉 Demo completed successfully!")
        show_excel_setup_guide()
    else:
        print("\n⚠️ Demo had issues - check dependencies")
    
    print("\n" + "=" * 60)
    print("Ready to start generating qualified leads! 🎯")

if __name__ == "__main__":
    main()
