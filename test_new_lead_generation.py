#!/usr/bin/env python3
"""
Test New Lead Generation - Real Instagram Username Discovery + Lead Generation
This script tests the complete pipeline from username discovery to qualified leads.
"""

import sys
import os
import json
import time
import csv
import re
from datetime import datetime
from typing import List, Dict, Optional

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    import instaloader
    INSTALOADER_AVAILABLE = True
except ImportError:
    INSTALOADER_AVAILABLE = False

# Import our existing filtering system
try:
    from instagram_scraper import (
        IndustryClassifier,
        meets_follower_criteria,
        has_recent_posts,
        is_us_based_profile,
        is_target_industry,
        extract_contact_info
    )
    FILTERS_AVAILABLE = True
except ImportError:
    FILTERS_AVAILABLE = False

class NewLeadGenerator:
    """
    Complete lead generation system using real Instagram discovery.
    """
    
    def __init__(self):
        """Initialize the lead generator."""
        if INSTALOADER_AVAILABLE:
            self.loader = instaloader.Instaloader(
                download_pictures=False,
                download_videos=False,
                download_video_thumbnails=False,
                download_geotags=False,
                download_comments=False,
                save_metadata=False
            )
            logger.info("✅ Instaloader initialized")
        else:
            self.loader = None
            logger.warning("❌ Instaloader not available")
        
        if FILTERS_AVAILABLE:
            self.industry_classifier = IndustryClassifier()
            logger.info("✅ Industry classifier initialized")
        else:
            self.industry_classifier = None
            logger.warning("❌ Industry classifier not available")
    
    def get_test_usernames(self) -> List[str]:
        """
        Get test usernames that are likely to exist on Instagram.
        These are common username patterns that often exist.
        """
        # These are realistic usernames that commonly exist on Instagram
        test_usernames = [
            # Generic business/coaching terms (likely to exist)
            "lifecoach",
            "businesscoach", 
            "motivational",
            "entrepreneur",
            "author",
            "consultant",
            "speaker",
            "success",
            "mindset",
            "leadership",
            
            # Specific patterns that often exist
            "business_coach",
            "life_coach",
            "success_coach",
            "mindset_coach",
            "entrepreneur_life",
            "business_mentor",
            "leadership_coach",
            "motivational_speaker",
            "keynote_speaker",
            "business_author",
            
            # Known to exist (from previous testing)
            "instagram",  # For testing system
            "business",   # Generic business account
            "coaching",   # Generic coaching account
        ]
        
        logger.info(f"🎯 Testing with {len(test_usernames)} potential usernames")
        return test_usernames
    
    def validate_username(self, username: str) -> Optional[Dict]:
        """
        Validate a username and get profile data.
        
        Args:
            username: Instagram username to validate
            
        Returns:
            Profile data dictionary or None if invalid
        """
        if not self.loader:
            return None
        
        try:
            logger.info(f"📡 Checking @{username}...")
            
            profile = instaloader.Profile.from_username(self.loader.context, username)
            
            profile_data = {
                'username': profile.username,
                'full_name': profile.full_name,
                'biography': profile.biography,
                'followers': profile.followers,
                'following': profile.followees,
                'posts_count': profile.mediacount,
                'is_verified': profile.is_verified,
                'is_private': profile.is_private,
                'external_url': profile.external_url,
                'is_business_account': profile.is_business_account,
                'scraped_at': datetime.now().isoformat()
            }
            
            logger.info(f"   ✅ Valid - {profile_data['followers']:,} followers")
            logger.info(f"   👤 {profile_data['full_name']}")
            logger.info(f"   🔒 Private: {profile_data['is_private']}")
            
            return profile_data
            
        except instaloader.exceptions.ProfileNotExistsException:
            logger.info(f"   ❌ @{username} does not exist")
            return None
        except instaloader.exceptions.LoginRequiredException:
            logger.info(f"   ⚠️ @{username} requires login")
            return None
        except Exception as e:
            logger.info(f"   ❌ Error: {e}")
            return None
    
    def qualify_profile(self, profile_data: Dict) -> Dict:
        """
        Apply qualification criteria to a profile.
        
        Args:
            profile_data: Profile data dictionary
            
        Returns:
            Qualification results
        """
        if not FILTERS_AVAILABLE or not self.industry_classifier:
            return {
                'qualifies': False,
                'reason': 'Filters not available',
                'checks': {}
            }
        
        username = profile_data['username']
        bio = profile_data.get('biography', '') or ''
        
        # Convert to format expected by filters
        filter_format = {
            'edge_followed_by': {'count': profile_data['followers']},
            'edge_owner_to_timeline_media': {'count': profile_data['posts_count']},
            'biography': bio,
            'username': username,
            'full_name': profile_data.get('full_name', ''),
            'external_url': profile_data.get('external_url', ''),
            'is_verified': profile_data.get('is_verified', False)
        }
        
        # Apply qualification checks
        follower_check = meets_follower_criteria(filter_format, max_followers=10000)
        posts_check = has_recent_posts(filter_format)
        us_check = is_us_based_profile(filter_format)
        industry_check = is_target_industry(bio, self.industry_classifier)
        is_public = not profile_data.get('is_private', True)
        
        checks = {
            'followers': follower_check,
            'posts': posts_check,
            'us_based': us_check,
            'industry': industry_check,
            'is_public': is_public
        }
        
        qualifies = all(checks.values())
        
        logger.info(f"   🔍 Qualification for @{username}:")
        logger.info(f"      • Followers (100-10k): {'✅' if follower_check else '❌'} ({profile_data['followers']:,})")
        logger.info(f"      • Recent posts: {'✅' if posts_check else '❌'}")
        logger.info(f"      • US-based: {'✅' if us_check else '❌'}")
        logger.info(f"      • Target industry: {'✅' if industry_check else '❌'}")
        logger.info(f"      • Public profile: {'✅' if is_public else '❌'}")
        
        if qualifies:
            logger.info(f"   🎉 @{username} QUALIFIED!")
        else:
            failed = [k for k, v in checks.items() if not v]
            logger.info(f"   ❌ @{username} filtered out (failed: {', '.join(failed)})")
        
        return {
            'qualifies': qualifies,
            'checks': checks,
            'reason': 'Passed all checks' if qualifies else f"Failed: {', '.join([k for k, v in checks.items() if not v])}"
        }
    
    def extract_lead_data(self, profile_data: Dict, qualification: Dict) -> Dict:
        """
        Extract complete lead data from qualified profile.
        
        Args:
            profile_data: Profile data
            qualification: Qualification results
            
        Returns:
            Complete lead data
        """
        # Extract contact information
        contact_info = {}
        if FILTERS_AVAILABLE:
            bio = profile_data.get('biography', '') or ''
            external_url = profile_data.get('external_url', '') or ''
            contact_text = f"{bio} {external_url}"
            contact_info = extract_contact_info(contact_text)
        
        # Determine industry
        industry = "Unknown"
        if FILTERS_AVAILABLE and self.industry_classifier:
            bio = profile_data.get('biography', '') or ''
            try:
                industry_result = self.industry_classifier.predict(bio)
                if hasattr(industry_result, 'item'):
                    industry = industry_result.item()
                else:
                    industry = str(industry_result)
            except:
                industry = "Unknown"
        
        # Create complete lead data
        lead_data = {
            'username': profile_data['username'],
            'full_name': profile_data.get('full_name', ''),
            'followers': profile_data['followers'],
            'following': profile_data['following'],
            'posts_count': profile_data['posts_count'],
            'biography': profile_data.get('biography', ''),
            'is_verified': profile_data.get('is_verified', False),
            'is_private': profile_data.get('is_private', True),
            'external_url': profile_data.get('external_url', ''),
            'is_business_account': profile_data.get('is_business_account', False),
            'industry': industry,
            'email': contact_info.get('emails', [''])[0] if contact_info.get('emails') else '',
            'phone': contact_info.get('phones', [''])[0] if contact_info.get('phones') else '',
            'website': contact_info.get('websites', [''])[0] if contact_info.get('websites') else '',
            'qualification_checks': qualification['checks'],
            'scraped_at': profile_data['scraped_at']
        }
        
        return lead_data
    
    def generate_new_leads(self, max_leads: int = 10) -> List[Dict]:
        """
        Generate new qualified leads from real Instagram profiles.
        
        Args:
            max_leads: Maximum number of qualified leads to find
            
        Returns:
            List of qualified lead data
        """
        logger.info(f"🚀 Starting New Lead Generation")
        logger.info(f"🎯 Target: {max_leads} qualified leads")
        logger.info(f"📊 Criteria: 100-10k followers, US-based, target industries, public profiles")
        
        test_usernames = self.get_test_usernames()
        qualified_leads = []
        checked_count = 0
        
        for username in test_usernames:
            if len(qualified_leads) >= max_leads:
                break
            
            checked_count += 1
            logger.info(f"\n[{checked_count}/{len(test_usernames)}] Testing @{username}")
            
            # Validate profile
            profile_data = self.validate_username(username)
            if not profile_data:
                continue
            
            # Apply qualification
            qualification = self.qualify_profile(profile_data)
            
            if qualification['qualifies']:
                # Extract complete lead data
                lead_data = self.extract_lead_data(profile_data, qualification)
                qualified_leads.append(lead_data)
                
                logger.info(f"✅ QUALIFIED LEAD #{len(qualified_leads)}: @{username}")
                logger.info(f"   Industry: {lead_data['industry']}")
                logger.info(f"   Email: {lead_data['email'] or 'Not found'}")
                logger.info(f"   Followers: {lead_data['followers']:,}")
            
            # Rate limiting
            time.sleep(2)
        
        logger.info(f"\n🎉 Lead generation complete!")
        logger.info(f"📊 Results: {len(qualified_leads)} qualified leads from {checked_count} profiles checked")
        logger.info(f"📈 Success rate: {len(qualified_leads)/checked_count*100:.1f}%")
        
        return qualified_leads
    
    def save_leads_to_csv(self, leads: List[Dict], filename: str = "new_qualified_leads.csv"):
        """
        Save qualified leads to CSV file.
        
        Args:
            leads: List of lead data
            filename: Output filename
        """
        if not leads:
            logger.warning("No leads to save")
            return
        
        headers = [
            'username', 'full_name', 'followers', 'following', 'posts_count',
            'biography', 'industry', 'email', 'phone', 'website',
            'is_verified', 'is_business_account', 'external_url', 'scraped_at'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()
            
            for lead in leads:
                row = {header: lead.get(header, '') for header in headers}
                writer.writerow(row)
        
        logger.info(f"💾 Saved {len(leads)} leads to {filename}")
        return filename

def show_lead_summary(leads: List[Dict]):
    """Show summary of generated leads."""
    if not leads:
        print("❌ No qualified leads found")
        return
    
    print(f"\n🎯 QUALIFIED LEADS SUMMARY")
    print("=" * 50)
    print(f"Total qualified leads: {len(leads)}")
    
    # Industry breakdown
    industries = {}
    emails_found = 0
    total_followers = 0
    
    for lead in leads:
        industry = lead.get('industry', 'Unknown')
        industries[industry] = industries.get(industry, 0) + 1
        if lead.get('email'):
            emails_found += 1
        total_followers += lead.get('followers', 0)
    
    print(f"\n📊 Industry Breakdown:")
    for industry, count in industries.items():
        print(f"   • {industry}: {count} leads")
    
    print(f"\n📈 Statistics:")
    print(f"   • Average followers: {total_followers // len(leads):,}")
    print(f"   • Leads with email: {emails_found} ({emails_found/len(leads)*100:.0f}%)")
    print(f"   • All profiles are public and in target follower range")
    
    print(f"\n🎯 SAMPLE LEADS:")
    for i, lead in enumerate(leads[:5], 1):
        print(f"{i}. @{lead['username']} - {lead.get('full_name', 'No name')}")
        print(f"   Industry: {lead.get('industry', 'Unknown')}")
        print(f"   Followers: {lead.get('followers', 0):,}")
        print(f"   Email: {lead.get('email', 'Not found')}")
        print(f"   Bio: {lead.get('biography', 'No bio')[:50]}...")
        print()

def main():
    """Main function to test new lead generation."""
    print("🚀 Testing New Lead Generation System")
    print("=" * 60)
    print("Testing complete pipeline: Username Discovery → Validation → Qualification → Lead Export")
    print()
    
    if not INSTALOADER_AVAILABLE:
        print("❌ Instaloader not available!")
        print("Install with: pip install instaloader")
        return False
    
    if not FILTERS_AVAILABLE:
        print("❌ Filtering system not available!")
        print("Make sure instagram_scraper.py is in the same directory")
        return False
    
    # Initialize lead generator
    generator = NewLeadGenerator()
    
    # Generate leads
    leads = generator.generate_new_leads(max_leads=10)
    
    if leads:
        # Save to CSV
        filename = generator.save_leads_to_csv(leads)
        
        # Show summary
        show_lead_summary(leads)
        
        print(f"\n📁 Results saved to: {filename}")
        print(f"💡 You can open this file in Excel or any spreadsheet app")
        
        print(f"\n🎯 NEXT STEPS:")
        print("1. Review the qualified leads in the CSV file")
        print("2. Use these real usernames for larger-scale discovery")
        print("3. Apply Google search to find more similar profiles")
        print("4. Start outreach campaigns with qualified leads")
        print("5. Scale up the system for 50+ leads")
        
        return True
    else:
        print("❌ No qualified leads found in this test")
        print("💡 This is normal - try:")
        print("1. Using Google search to find more specific usernames")
        print("2. Adjusting qualification criteria")
        print("3. Testing with different username patterns")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
