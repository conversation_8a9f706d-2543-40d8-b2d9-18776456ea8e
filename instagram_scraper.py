# Instagram Scraper Main File

# --- IMPORTANT ---
# GOOGLE SHEETS API AUTHENTICATION:
# This script requires a 'creds.json' file for authenticating with the Google Sheets API.
# 1. Obtain your service account key (JSON file) from the Google Cloud Console:
#    - Go to IAM & Admin -> Service Accounts.
#    - Select your project.
#    - Choose or create a service account.
#    - For the chosen service account, go to "Keys".
#    - Click "Add Key" -> "Create new key" -> Select "JSON" and "Create".
#    - This will download the JSON key file.
# 2. Rename this downloaded file to 'creds.json'.
# 3. Place 'creds.json' in the same directory as this script (`instagram_scraper.py`).
# 4. Ensure the Google Sheets API is enabled for your project in Google Cloud Console.
# 5. Share the target Google Sheet with the service account's email address
#    (found in the 'client_email' field within 'creds.json'), giving it editor permissions.
# --- --- --- --- ---

import requests
import logging
import re
from pydantic import BaseModel, ValidationError
from typing import Optional
from datetime import datetime
import time
import random

# Configure logging with a more detailed format
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s'
)
logger = logging.getLogger(__name__)

class InstagramProfile(BaseModel):
    """
    Pydantic model for storing Instagram profile data.
    """
    username: str
    full_name: str 
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    followers: int
    following: int
    posts_count: Optional[int] = None
    verified_email: Optional[str] = None # Renamed from email
    phone: Optional[str] = None
    industry: Optional[str] = None
    profile_url: str
    is_verified: bool
    scraped_at: datetime = datetime.utcnow() # Default value, will be set at instantiation

def get_instagram_profile(username: str, proxy: dict, headers: dict) -> dict:
    """
    Fetches Instagram profile data.

    Args:
        username: The Instagram username.
        proxy: The proxy to use for the request.
        headers: The headers to use for the request.

    Returns:
        The user data as a dictionary, or an empty dictionary if an error occurs.
    """
    url = f"https://www.instagram.com/api/v1/users/web_profile_info/?username={username}"
    try:
        response = requests.get(url, headers=headers, proxies=proxy, timeout=10)
        response.raise_for_status()  # Raise an exception for bad status codes
        return response.json()["data"]["user"]
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching profile for {username}: {e}")
        return {}
    except KeyError as e:
        logger.error(f"Error parsing profile data for {username}: Missing key {e}")
        return {}

def extract_contact_info(profile: dict) -> dict:
    """
    Extracts contact information (emails and phone numbers) from a profile.

    Args:
        profile: The user profile data.

    Returns:
        A dictionary containing lists of emails and phone numbers.
    """
    contact_info = {"emails": [], "phones": []}
    if not profile:
        logger.debug("Profile data is empty for contact extraction.")
        return contact_info

    text_sources = [profile.get("biography", ""), profile.get("external_url", "")]
    text_sources = [source for source in text_sources if source] # Remove None or empty strings

    for text in text_sources:
        # Regex for emails
        emails = re.findall(r'[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}', text)
        contact_info["emails"].extend(emails)

        # Regex for phone numbers
        # This regex is a common pattern, but might need adjustments for specific international formats
        phones = re.findall(r'(?:\+?(\d{1,3}))?[-. (]*(\d{3})[-. )]*(\d{3})[-. ]*(\d{4})', text)
        # re.findall returns tuples for phone numbers due to capturing groups, format them
        formatted_phones = []
        for phone_parts in phones:
            # Filter out empty strings from parts and join
            formatted_phone = "".join(filter(None, phone_parts))
            if formatted_phone: # Ensure we don't add empty strings if all parts were None/empty
                 # Add a leading '+' if it's an international number format that lost it
                if len(phone_parts[0]) > 0 and not formatted_phone.startswith('+'):
                    formatted_phone = '+' + formatted_phone
            formatted_phones.append(formatted_phone)
        contact_info["phones"].extend(formatted_phones)
        
    # Remove duplicates
    contact_info["emails"] = list(set(contact_info["emails"]))
    contact_info["phones"] = list(set(contact_info["phones"]))

    return contact_info

from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB

class IndustryClassifier:
    """
    Classifies industry based on biography text using a Naive Bayes model.
    """
    def __init__(self):
        self.vectorizer = TfidfVectorizer(stop_words='english')
        self.model = MultinomialNB()
        self._train_model()
        logger.info("IndustryClassifier initialized and model trained.")

    def _train_model(self):
        """
        Trains the Naive Bayes model with predefined industry data focused on target industries.
        """
        industry_data = {
            "keynote_speaker": [
                "keynote speaker", "motivational speaker", "public speaker", "inspirational speaker",
                "conference speaker", "event speaker", "professional speaker", "speaker bureau",
                "speaking engagements", "corporate speaker", "leadership speaker", "TEDx speaker",
                "workshop facilitator", "seminar leader", "presentation expert"
            ],
            "coach_consultant_creator": [
                "life coach", "business coach", "executive coach", "career coach", "mindset coach",
                "online coach", "consultant", "business consultant", "marketing consultant",
                "course creator", "online course", "digital course", "coaching program",
                "mastermind", "group coaching", "1-on-1 coaching", "transformation coach",
                "success coach", "entrepreneur coach", "leadership coach", "sales coach",
                "marketing strategist", "business strategist", "online entrepreneur",
                "digital marketing", "course launch", "coaching business", "online business"
            ],
            "author": [
                "author", "writer", "bestselling author", "published author", "book author",
                "novelist", "non-fiction author", "self-help author", "business author",
                "motivational author", "inspirational author", "thought leader",
                "content writer", "copywriter", "ghostwriter", "editor", "publisher",
                "book coach", "writing coach", "manuscript", "bestseller", "amazon author",
                "kindle author", "self-published", "traditional publishing"
            ]
        }
        
        documents = []
        labels = []
        for industry, keywords in industry_data.items():
            for keyword in keywords:
                documents.append(keyword)
                labels.append(industry)
        
        X = self.vectorizer.fit_transform(documents)
        self.model.fit(X, labels)
        logger.info("TfidfVectorizer and MultinomialNB model trained successfully.")

    def predict(self, bio: str) -> str:
        """
        Predicts the industry for a given biography.

        Args:
            bio: The biography text.

        Returns:
            The predicted industry label or "unknown" if bio is empty.
        """
        if not bio:
            return "unknown"
        
        features = self.vectorizer.transform([bio])
        return self.model.predict(features)[0]

try:
    import pandas as pd
    import openpyxl
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    logger.warning("Excel support not available. Install 'pandas' and 'openpyxl' for Excel functionality.")

try:
    import gspread
    from google.oauth2.service_account import Credentials
    GSHEETS_AVAILABLE = True
except ImportError:
    GSHEETS_AVAILABLE = False
    logger.warning("Google Sheets support not available. Install 'gspread' and 'google-auth' for Google Sheets functionality.")

class ExcelHandler:
    """
    Handles operations with Excel files for storing Instagram profile data.
    """
    def __init__(self, file_path: str = "instagram_leads.xlsx"):
        if not EXCEL_AVAILABLE:
            raise ImportError("Excel support not available. Install 'pandas' and 'openpyxl'")

        self.file_path = file_path
        self.df = None
        self._load_or_create_file()
        logger.info(f"ExcelHandler initialized with file: {file_path}")

    def _load_or_create_file(self):
        """
        Loads existing Excel file or creates a new one with headers.
        """
        try:
            # Try to load existing file
            self.df = pd.read_excel(self.file_path)
            logger.info(f"Loaded existing Excel file with {len(self.df)} records")
        except FileNotFoundError:
            # Create new DataFrame with headers based on InstagramProfile fields
            headers = list(InstagramProfile.__annotations__.keys())
            self.df = pd.DataFrame(columns=headers)
            self._save_file()
            logger.info("Created new Excel file with headers")

    def _save_file(self):
        """
        Saves the DataFrame to Excel file.
        """
        try:
            self.df.to_excel(self.file_path, index=False, engine='openpyxl')
            logger.debug(f"Saved Excel file: {self.file_path}")
        except Exception as e:
            logger.error(f"Error saving Excel file: {e}")

    def append_profile(self, profile: InstagramProfile):
        """
        Appends a single Instagram profile to the Excel file.

        Args:
            profile: An InstagramProfile Pydantic model instance.
        """
        try:
            # Convert profile to dictionary
            profile_dict = {
                'username': profile.username,
                'full_name': profile.full_name,
                'first_name': profile.first_name,
                'last_name': profile.last_name,
                'followers': profile.followers,
                'following': profile.following,
                'posts_count': profile.posts_count,
                'verified_email': profile.verified_email,
                'phone': profile.phone,
                'industry': profile.industry,
                'profile_url': profile.profile_url,
                'is_verified': profile.is_verified,
                'scraped_at': profile.scraped_at.isoformat() if profile.scraped_at else None
            }

            # Add to DataFrame
            new_row = pd.DataFrame([profile_dict])
            self.df = pd.concat([self.df, new_row], ignore_index=True)

            # Save to file
            self._save_file()
            logger.info(f"Appended profile for {profile.username} to Excel file")

        except Exception as e:
            logger.error(f"Error appending profile for {profile.username} to Excel file: {e}")

    def remove_profile(self, email: str):
        """
        Removes a profile from the Excel file based on the email address.

        Args:
            email: The email address of the profile to remove.
        """
        try:
            initial_count = len(self.df)
            self.df = self.df[self.df['verified_email'] != email]
            removed_count = initial_count - len(self.df)

            if removed_count > 0:
                self._save_file()
                logger.info(f"Removed {removed_count} profile(s) with email: {email}")
            else:
                logger.info(f"No profile found with email: {email} to remove")

        except Exception as e:
            logger.error(f"Error removing profile with email {email}: {e}")

    def get_stats(self):
        """
        Returns statistics about the stored data.
        """
        if self.df is None or len(self.df) == 0:
            return {"total_profiles": 0}

        stats = {
            "total_profiles": len(self.df),
            "industries": self.df['industry'].value_counts().to_dict() if 'industry' in self.df.columns else {},
            "verified_profiles": len(self.df[self.df['is_verified'] == True]) if 'is_verified' in self.df.columns else 0,
            "profiles_with_email": len(self.df[self.df['verified_email'].notna()]) if 'verified_email' in self.df.columns else 0,
            "avg_followers": self.df['followers'].mean() if 'followers' in self.df.columns else 0
        }
        return stats

class GoogleSheetsHandler:
    """
    Handles operations with Google Sheets for storing Instagram profile data.
    """
    def __init__(self, creds_file: str, sheet_name: str):
        self.scope = ['https://www.googleapis.com/auth/spreadsheets']
        try:
            self.creds = Credentials.from_service_account_file(creds_file, scopes=self.scope)
            self.client = gspread.authorize(self.creds)
            self.sheet = self.client.open(sheet_name).sheet1
            self._ensure_headers()
        except FileNotFoundError:
            logger.error(f"Credentials file not found at {creds_file}. Please ensure it's in the correct path.", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"Error initializing GoogleSheetsHandler: {e}", exc_info=True)
            raise

    def _ensure_headers(self):
        """
        Ensures that the Google Sheet has the correct headers.
        If the sheet is empty, it appends the headers based on InstagramProfile fields.
        """
        if not self.sheet.get_all_values():
            # Ensure 'scraped_at' is part of the header list if it comes from annotations
            headers = list(InstagramProfile.__annotations__.keys())
            self.sheet.append_row(headers)
            logger.info("Headers added to the Google Sheet.")

    def append_profile(self, profile: InstagramProfile):
        """
        Appends a single Instagram profile to the Google Sheet.

        Args:
            profile: An InstagramProfile Pydantic model instance.
        """
        # Order should match the InstagramProfile model definition for consistency with headers
        row_data = [
            profile.username,
            profile.full_name,
            profile.first_name,
            profile.last_name,
            profile.followers,
            profile.following,
            profile.posts_count,
            profile.verified_email,
            profile.phone,
            profile.industry,
            profile.profile_url,
            profile.is_verified,
            profile.scraped_at.isoformat() if profile.scraped_at else None
        ]
        try:
            self.sheet.append_row(row_data)
            logger.info(f"Appended profile for {profile.username} to Google Sheet.")
        except Exception as e:
            logger.error(f"Error appending profile for {profile.username} to Google Sheet: {e}", exc_info=True)

    def remove_profile(self, email: str):
        """
        Removes a profile from the Google Sheet based on the email address.

        Args:
            email: The email address of the profile to remove.
        """
        try:
            records = self.sheet.get_all_records() # This gets records as dicts
            # Ensure headers are up-to-date if sheet was created with old model
            # However, get_all_records uses current headers from the sheet
            for idx, record in enumerate(records, start=2): # Sheets are 1-indexed, +1 for header
                if record.get('verified_email') == email: # Changed from 'email' to 'verified_email'
                    self.sheet.delete_rows(idx) 
                    logger.info(f"Removed profile with verified_email: {email} from row {idx}")
                    return 
            logger.info(f"No profile found with verified_email: {email} to remove.")
        except Exception as e:
            logger.error(f"Error removing profile with email {email}: {e}", exc_info=True)

from datetime import timedelta # datetime is already imported

class ComplianceManager:
    """
    Manages compliance aspects like opt-out requests and data retention.
    """
    def __init__(self, data_handler):
        """
        Initialize with either ExcelHandler or GoogleSheetsHandler
        """
        self.data_handler = data_handler
        self.blocklist = set()
        handler_type = type(data_handler).__name__
        logger.info(f"ComplianceManager initialized with {handler_type}.")

    def process_opt_out(self, email: str):
        """
        Processes an opt-out request by removing the profile from data storage
        and adding the email to a blocklist.

        Args:
            email: The email address to process for opt-out.
        """
        if not email:
            logger.warning("Opt-out request received with no email.")
            return

        self.data_handler.remove_profile(email) # This already logs
        self.blocklist.add(email.lower()) # Store emails in a consistent case
        logger.info(f"Processed opt-out for {email}. Added to blocklist.")

    def enforce_retention_policy(self, max_days: int = 30):
        """
        Enforces data retention policy by removing records older than max_days
        from the Google Sheet.

        Args:
            max_days: The maximum number of days to retain records.
        """
        logger.info(f"Starting retention policy enforcement (max_days: {max_days})...")
        try:
            all_rows = self.sheets.sheet.get_all_values()
            if not all_rows or len(all_rows) <= 1:  # No data or only headers
                logger.info("No records found to enforce retention policy on.")
                return

            headers = all_rows[0]
            records = all_rows[1:] # Data rows

            try:
                scraped_at_index = headers.index("scraped_at")
            except ValueError:
                logger.error("'scraped_at' column not found in sheet headers. Cannot enforce retention policy.")
                return

            cutoff_date = datetime.utcnow() - timedelta(days=max_days)
            rows_to_delete_indices = [] # Store 1-based indices for gspread

            for i, record_row in enumerate(records, start=2): # start=2 because sheet is 1-indexed and row 1 is header
                if len(record_row) <= scraped_at_index or not record_row[scraped_at_index]:
                    logger.warning(f"Row {i}: 'scraped_at' data is missing or malformed. Skipping.")
                    continue
                
                scraped_at_str = record_row[scraped_at_index]
                try:
                    # Attempt to parse ISO format, then try other common formats if necessary
                    scraped_datetime = datetime.fromisoformat(scraped_at_str.replace("Z", "+00:00"))
                     # Ensure timezone-aware comparison if necessary, or make cutoff timezone-aware
                    if scraped_datetime.tzinfo is None:
                         # Assuming UTC if no timezone info, consistent with datetime.utcnow()
                        scraped_datetime = scraped_datetime.replace(tzinfo=None) # Make it naive for direct comparison with naive cutoff_date
                    
                except ValueError:
                    logger.warning(f"Row {i}: Could not parse 'scraped_at' value '{scraped_at_str}'. Skipping.")
                    continue
                
                # Compare naive datetimes (both should be UTC)
                if scraped_datetime < cutoff_date.replace(tzinfo=None):
                    rows_to_delete_indices.append(i)
            
            if rows_to_delete_indices:
                # Delete rows in reverse order to avoid index shifting issues
                for row_idx in sorted(rows_to_delete_indices, reverse=True):
                    self.sheets.sheet.delete_rows(row_idx) # delete_rows is 1-indexed
                logger.info(f"Enforced retention policy. Removed {len(rows_to_delete_indices)} old records.")
            else:
                logger.info("No records found older than the retention period.")

        except gspread.exceptions.APIError as e:
            logger.error(f"Google Sheets API error during retention policy enforcement: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"An unexpected error occurred during retention policy enforcement: {e}", exc_info=True)

class ProxyRotator:
    """
    Manages a list of proxies and rotates through them.
    Each proxy in the input list should be a dict with 'host', 'port', 'user', 'pass'.
    """
    def __init__(self, proxies: list):
        if not proxies:
            # This error is critical for ProxyRotator's function, so raising an exception is appropriate.
            # The logger.error call is good for recording, the raise informs the caller.
            logger.error("Proxy list cannot be empty for ProxyRotator.", exc_info=True) # Added exc_info for context
            raise ValueError("Proxy list cannot be empty.")
        self.proxies = proxies
        self.index = 0
        logger.info(f"ProxyRotator initialized with {len(proxies)} proxies.")

    def get_next_proxy(self) -> dict:
        """
        Returns the next proxy in the list in a format suitable for the requests library.
        Cycles through the proxy list.
        """
        if not self.proxies: # Should have been caught in __init__, but as a safeguard
            logger.warning("No proxies available to rotate.")
            return {}

        # Get the current proxy
        proxy_details = self.proxies[self.index]
        
        # Increment index for next call
        self.index = (self.index + 1) % len(self.proxies)
        
        # Format for requests library
        proxy_url = f"http://{proxy_details['user']}:{proxy_details['pass']}@{proxy_details['host']}:{proxy_details['port']}"
        
        logger.debug(f"Using proxy: {proxy_details['host']}:{proxy_details['port']}")
        return {
            "http": proxy_url,
            "https": proxy_url  # Typically the same proxy URL is used for HTTP and HTTPS
        }

USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Safari/605.1.15",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36"
]

def attempt_email_verification(email_address: Optional[str]) -> Optional[str]:
    """
    Placeholder function for email verification.
    Currently returns the email as is.
    """
    # Placeholder for actual email verification logic.
    # TODO: Integrate with a real email verification service (e.g., Apollo, Findymail)
    # when API key and service preference are provided by the user.
    # For now, it returns the email as is, assuming it's 'verified' for structure.
    if email_address:
        logger.debug(f"Attempting (placeholder) verification for email: {email_address}")
    return email_address

def is_us_based_profile(profile_data: dict) -> bool:
    """
    Determines if a profile appears to be US-based.

    Args:
        profile_data: The Instagram profile data

    Returns:
        True if profile appears to be US-based
    """
    if not profile_data:
        return False

    # Check biography for US indicators
    bio = profile_data.get('biography', '').lower()
    us_indicators = [
        'usa', 'united states', 'america', 'us based', 'based in us',
        'california', 'texas', 'florida', 'new york', 'illinois', 'pennsylvania',
        'ohio', 'georgia', 'north carolina', 'michigan', 'new jersey', 'virginia',
        'washington', 'arizona', 'massachusetts', 'tennessee', 'indiana', 'missouri',
        'maryland', 'wisconsin', 'colorado', 'minnesota', 'south carolina', 'alabama',
        'louisiana', 'kentucky', 'oregon', 'oklahoma', 'connecticut', 'utah', 'iowa',
        'nevada', 'arkansas', 'mississippi', 'kansas', 'new mexico', 'nebraska',
        'west virginia', 'idaho', 'hawaii', 'new hampshire', 'maine', 'montana',
        'rhode island', 'delaware', 'south dakota', 'north dakota', 'alaska', 'vermont',
        'wyoming', 'los angeles', 'chicago', 'houston', 'phoenix', 'philadelphia',
        'san antonio', 'san diego', 'dallas', 'san jose', 'austin', 'jacksonville',
        'fort worth', 'columbus', 'charlotte', 'san francisco', 'indianapolis',
        'seattle', 'denver', 'washington dc', 'boston', 'nashville', 'baltimore',
        'oklahoma city', 'portland', 'las vegas', 'milwaukee', 'albuquerque',
        'tucson', 'fresno', 'sacramento', 'mesa', 'kansas city', 'atlanta', 'omaha',
        'colorado springs', 'raleigh', 'miami', 'virginia beach', 'oakland', 'minneapolis',
        'tulsa', 'arlington', 'tampa', 'new orleans', 'wichita', 'cleveland', 'bakersfield',
        'aurora', 'anaheim', 'honolulu', 'santa ana', 'corpus christi', 'riverside',
        'lexington', 'stockton', 'toledo', 'st. paul', 'newark', 'anchorage', 'plano',
        'fort wayne', 'st. petersburg', 'glendale', 'lincoln', 'norfolk', 'jersey city',
        'greensboro', 'chandler', 'birmingham', 'henderson', 'scottsdale', 'north las vegas',
        'madison', 'buffalo', 'chesapeake', 'orlando', 'garland', 'detroit', 'memphis',
        'san bernardino', 'modesto', 'des moines', 'rochester', 'tacoma', 'fontana',
        'oxnard', 'moreno valley', 'huntington beach', 'salt lake city', 'grand rapids',
        'amarillo', 'yonkers', 'aurora', 'montgomery', 'akron', 'little rock', 'huntsville',
        'augusta', 'port st. lucie', 'grand prairie', 'columbus', 'tallahassee',
        'overland park', 'tempe', 'mckinney', 'mobile', 'cape coral', 'shreveport',
        'frisco', 'knoxville', 'worcester', 'brownsville', 'vancouver', 'fort lauderdale',
        'sioux falls', 'ontario', 'chattanooga', 'providence', 'newport news', 'rancho cucamonga',
        'santa clarita', 'peoria', 'elk grove', 'salem', 'pembroke pines', 'eugene',
        'garden grove', 'cary', 'fort collins', 'corona', 'springfield', 'jackson',
        'alexandria', 'hayward', 'clarksville', 'lakewood', 'lancaster', 'salinas',
        'palmdale', 'hollywood', 'springfield', 'macon', 'kansas city', 'sunnyvale',
        'pomona', 'killeen', 'escondido', 'pasadena', 'naperville', 'bellevue',
        'joliet', 'murfreesboro', 'midland', 'rockford', 'paterson', 'savannah',
        'bridgeport', 'torrance', 'mcallen', 'syracuse', 'allentown', 'carrollton',
        'beaumont', 'wyoming', 'fullerton', 'warren', 'west valley city', 'columbia',
        'inglewood', 'sterling heights', 'new haven', 'miramar', 'waco', 'thousand oaks',
        'cedar rapids', 'charleston', 'visalia', 'topeka', 'elizabeth', 'gainesville',
        'thornton', 'roseville', 'carmel', 'coral springs', 'stamford', 'simi valley',
        'concord', 'hartford', 'kent', 'lafayette', 'midland', 'surprise', 'denton',
        'victorville', 'evansville', 'santa clara', 'abilene', 'athens', 'vallejo',
        'fargo', 'arvada', 'elgin', 'mesa', 'Westminster', 'norman', 'centennial',
        'ventura', 'high point', 'miami gardens', 'downey', 'temecula', 'pueblo',
        'overland park', 'peoria', 'livonia', 'roswell', 'brockton', 'richmond',
        'baton rouge', 'rialto', 'el monte', 'jurupa valley', 'el paso', 'burbank'
    ]

    # Check for US indicators in bio
    for indicator in us_indicators:
        if indicator in bio:
            logger.debug(f"Found US indicator in bio: {indicator}")
            return True

    # Check external URL for US domains
    external_url = profile_data.get('external_url', '').lower()
    if external_url:
        us_domains = ['.us', 'usa', 'america', 'united-states']
        for domain in us_domains:
            if domain in external_url:
                logger.debug(f"Found US indicator in URL: {domain}")
                return True

    # If no clear indicators, return False (we want to be conservative)
    return False

def has_recent_posts(profile_data: dict, days_threshold: int = 30) -> bool:
    """
    Checks if the profile has recent posting activity.

    Args:
        profile_data: The Instagram profile data
        days_threshold: Number of days to consider as "recent"

    Returns:
        True if profile has recent posts
    """
    # Note: The basic web API doesn't provide post timestamps
    # This is a placeholder that assumes if they have posts, they're recent
    # In a full implementation, you'd need to check individual post timestamps

    posts_count = profile_data.get('edge_owner_to_timeline_media', {}).get('count', 0)

    # If they have posts, we assume some are recent
    # This is a limitation of the basic API - for more accurate checking,
    # you'd need to fetch individual posts
    if posts_count > 0:
        logger.debug(f"Profile has {posts_count} posts, assuming recent activity")
        return True

    return False

def meets_follower_criteria(profile_data: dict, max_followers: int = 10000) -> bool:
    """
    Checks if profile meets follower count criteria.

    Args:
        profile_data: The Instagram profile data
        max_followers: Maximum follower count

    Returns:
        True if profile meets criteria
    """
    followers = profile_data.get('edge_followed_by', {}).get('count', 0)

    # Must have some followers but less than max
    if 100 <= followers <= max_followers:  # At least 100 followers to show some engagement
        logger.debug(f"Profile has {followers} followers, meets criteria")
        return True

    logger.debug(f"Profile has {followers} followers, does not meet criteria (100-{max_followers})")
    return False

def is_target_industry(bio: str, industry_classifier) -> bool:
    """
    Checks if the profile belongs to one of our target industries.

    Args:
        bio: Profile biography text
        industry_classifier: Trained industry classifier

    Returns:
        True if profile is in target industry
    """
    if not bio:
        return False

    predicted_industry = industry_classifier.predict(bio)
    target_industries = ['keynote_speaker', 'coach_consultant_creator', 'author']

    is_target = predicted_industry in target_industries
    logger.debug(f"Industry prediction: {predicted_industry}, is target: {is_target}")

    return is_target

def scrape_profiles(usernames: list[str], proxy_service: ProxyRotator, data_handler, industry_clf: IndustryClassifier, compliance: ComplianceManager):
    """
    Scrapes Instagram profiles for a list of usernames.

    Args:
        usernames: A list of Instagram usernames to scrape.
        proxy_service: An instance of ProxyRotator.
        data_handler: An instance of ExcelHandler or GoogleSheetsHandler.
        industry_clf: An instance of IndustryClassifier.
        compliance: An instance of ComplianceManager.
    """
    if not usernames:
        logger.info("Usernames list is empty. Nothing to scrape.")
        return

    logger.info(f"Starting to scrape {len(usernames)} profiles.")

    for username in usernames:
        try:
            logger.info(f"Processing username: {username}")

            # 1. Proxy & Throttling
            if proxy_service:
                proxy = proxy_service.get_next_proxy()
                if not proxy:
                    logger.error("No proxy available from proxy service. Stopping.")
                    # Depending on desired behavior, could raise an error or break
                    break 
            else:
                proxy = {} # No proxy service provided, run without proxy
            
            sleep_time = random.uniform(1.5, 3.5)
            logger.debug(f"Sleeping for {sleep_time:.2f} seconds before request for {username}.")
            time.sleep(sleep_time)

            # 2. Fetch Profile Data
            headers = {
                "User-Agent": random.choice(USER_AGENTS),
                "x-ig-app-id": "936619743392459" # Common public App ID for web requests
            }
            
            profile_data = get_instagram_profile(username, proxy, headers)

            if not profile_data:
                logger.warning(f"Could not retrieve profile data for {username}. Skipping.")
                continue

            # 3. Apply Target Criteria Filters
            bio_text = profile_data.get('biography', '')

            # Check if profile meets all criteria
            if not meets_follower_criteria(profile_data):
                logger.info(f"Skipping {username}: doesn't meet follower criteria (100-10k)")
                continue

            if not has_recent_posts(profile_data):
                logger.info(f"Skipping {username}: no recent posting activity")
                continue

            if not is_us_based_profile(profile_data):
                logger.info(f"Skipping {username}: doesn't appear to be US-based")
                continue

            if not is_target_industry(bio_text, industry_clf):
                logger.info(f"Skipping {username}: not in target industry")
                continue

            logger.info(f"✅ {username} meets all criteria - proceeding with data extraction")

            # 4. Extract Contact Info
            contact_info = extract_contact_info(profile_data)
            raw_primary_email = contact_info['emails'][0] if contact_info['emails'] else None
            primary_phone = contact_info['phones'][0] if contact_info['phones'] else None

            # Attempt email verification
            processed_email = attempt_email_verification(raw_primary_email)

            # 5. Compliance Check (using the original raw email for blocklist lookup for consistency)
            if raw_primary_email and raw_primary_email.lower() in compliance.blocklist:
                logger.info(f"Skipping {username} as their email {raw_primary_email} is on the blocklist.")
                continue
            
            # Check blocklist for username itself if no email (optional, based on requirements)
            # if username.lower() in compliance.blocklist_usernames: # Assuming a separate blocklist for usernames
            #     logger.info(f"Skipping {username} as they are on the username blocklist.")
            #     continue

            # 6. Create Validated Profile (Pydantic model)
            # Ensure all fields are present or have defaults
            if bio_text is None: # Handle cases where biography might be explicitly None
                bio_text = ''
            
            current_full_name = profile_data.get('full_name', '')
            first_name_val = None
            last_name_val = None
            if current_full_name:
                name_parts = current_full_name.split(" ", 1)
                first_name_val = name_parts[0]
                if len(name_parts) > 1:
                    last_name_val = name_parts[1]

            posts_count_val = profile_data.get('edge_owner_to_timeline_media', {}).get('count', None) # Allow None if not found

            profile = InstagramProfile(
                username=username, # Use the input username for consistency
                full_name=current_full_name,
                first_name=first_name_val,
                last_name=last_name_val,
                followers=profile_data.get('edge_followed_by', {}).get('count', 0),
                following=profile_data.get('edge_follow', {}).get('count', 0),
                posts_count=posts_count_val,
                verified_email=processed_email, # Use the processed (placeholder verified) email
                phone=primary_phone,
                industry=industry_clf.predict(bio_text),
                profile_url=f"https://instagram.com/{username}",
                is_verified=profile_data.get('is_verified', False)
                # scraped_at is set by default in Pydantic model
            )

            # 7. Save to Data Storage (Excel or Google Sheets)
            data_handler.append_profile(profile)
            logger.info(f"Successfully processed and saved profile: {username}")

        except ValidationError as ve:
            logger.error(f"Pydantic validation error for {username}: {ve}")
        except requests.exceptions.HTTPError as http_err:
            logger.error(f"HTTP error for {username}: {http_err} - Response: {http_err.response.text if http_err.response else 'No response text'}")
        except requests.exceptions.ConnectionError as conn_err:
            logger.error(f"Connection error for {username} (proxy issue?): {conn_err}")
        except requests.exceptions.Timeout:
            logger.error(f"Request timed out for {username}.")
        except Exception as e:
            logger.error(f"An unexpected error occurred while processing {username}: {e}", exc_info=True) # exc_info=True for traceback

    logger.info("Finished scraping all provided usernames.")

if __name__ == "__main__":
    logger.info("Instagram Scraper script started.")

    # IMPORTANT: Replace with your actual proxy details or ensure your proxy provider's format is matched.
    # The proxy list should contain dictionaries with 'host', 'port', 'user', 'pass'.
    # If you don't want to use proxies, set example_proxies = [] and handle ProxyRotator instantiation.
    example_proxies = [
        # {'host': 'proxy1.example.com', 'port': '8000', 'user': 'user1', 'pass': 'pass1'},
        # {'host': 'proxy2.example.com', 'port': '8000', 'user': 'user2', 'pass': 'pass2'}
    ]

    proxy_service = None
    if example_proxies: # Only instantiate if there are proxies
        try:
            proxy_service = ProxyRotator(proxies=example_proxies)
            logger.info("ProxyRotator initialized.")
        except ValueError as ve:
            logger.warning(f"Could not initialize ProxyRotator: {ve}. Continuing without proxies.")
            # proxy_service will remain None, scrape_profiles handles this
    else:
        logger.info("No proxies provided in example_proxies list. Running without proxies.")


    # Define target usernames to scrape - these should be real usernames of your target audience
    target_usernames = [
        # Real Instagram usernames for testing (these are public profiles)
        "garyvee",           # Business author and speaker
        "tonyrobbins",       # Life coach and speaker
        "melrobbins",        # Author and speaker
        "brendonburchard",   # High performance coach
        "amyporterfield",    # Online course creator
        "marieforleo",       # Business coach and author
        "lewishowes",        # Author and podcast host
        "jasonfeuersteinig", # Business coach
        "rachelhollis",      # Author and speaker
        "deangraziosi",      # Real estate coach and author
    ]

    if not target_usernames:
        logger.warning("No target usernames defined. Please add Instagram usernames to scrape.")
        logger.info("Example: target_usernames = ['lifecoach_sarah', 'businessmentor_john', 'author_jane']")

    try:
        # Initialize core components
        industry_classifier = IndustryClassifier()
        logger.info("IndustryClassifier initialized.")

        # Initialize Excel handler (much simpler than Google Sheets!)
        excel_file_path = "instagram_leads_targeted.xlsx"

        try:
            if EXCEL_AVAILABLE:
                data_handler = ExcelHandler(file_path=excel_file_path)
                logger.info(f"ExcelHandler initialized. Data will be saved to: {excel_file_path}")
            else:
                logger.error("Excel support not available. Please install: pip install pandas openpyxl")
                # Fallback to Google Sheets if available
                if GSHEETS_AVAILABLE:
                    logger.info("Falling back to Google Sheets...")
                    data_handler = GoogleSheetsHandler(creds_file='creds.json', sheet_name='Instagram Leads Test')
                    logger.info("GoogleSheetsHandler initialized as fallback.")
                else:
                    logger.error("No data storage available. Please install Excel or Google Sheets dependencies.")
                    exit(1)
        except Exception as e:
            logger.error(f"Error initializing data handler: {e}")
            exit(1)

        # Initialize compliance manager
        compliance_manager = ComplianceManager(data_handler=data_handler)
        logger.info("ComplianceManager initialized.")

        if target_usernames:
            # Start scraping
            logger.info(f"🎯 Starting targeted scraping for {len(target_usernames)} profiles...")
            logger.info("Looking for: Keynote Speakers, Coaches/Consultants/Course Creators, Authors")
            logger.info("Criteria: < 10k followers, US-based, recent activity")

            scrape_profiles(
                usernames=target_usernames,
                proxy_service=proxy_service, # Can be None if proxies are not set up
                data_handler=data_handler,
                industry_clf=industry_classifier,
                compliance=compliance_manager
            )
            logger.info("Profile scraping process completed.")

            # Show statistics
            if hasattr(data_handler, 'get_stats'):
                stats = data_handler.get_stats()
                logger.info(f"📊 Results: {stats}")

            # Run compliance cleanup
            logger.info("Running retention policy enforcement...")
            compliance_manager.enforce_retention_policy(max_days=30) # Example: 30 days retention
            logger.info("Retention policy enforcement completed.")
        else:
            logger.info("No usernames to scrape. Please add target usernames to the list.")


    except Exception as e:
        logger.critical(f"A critical error occurred in the main execution block: {e}", exc_info=True)

    logger.info("Instagram Scraper script finished.")
