#!/usr/bin/env python3
"""
Run a live search test to see what leads we can find.
This will test the Instagram scraper with real profiles and show results.
"""

import sys
import os
import json
import time
import random
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_live_search():
    """Run a live search with real Instagram profiles."""
    print("🔍 LIVE INSTAGRAM LEAD SEARCH")
    print("=" * 60)
    print("Target: Keynote Speakers, Coaches/Consultants/Course Creators, Authors")
    print("Criteria: 100-10k followers, US-based, recent activity")
    print()
    
    # Test usernames - mix of different follower counts and industries
    test_usernames = [
        # These should have various follower counts to test our filtering
        "garyvee",           # Likely too many followers (will be filtered out)
        "tonyrobbins",       # Likely too many followers (will be filtered out)  
        "melrobbins",        # Author and speaker
        "br<PERSON><PERSON><PERSON>chard",   # High performance coach
        "amyporterfield",    # Online course creator
        "marieforleo",       # Business coach and author
        "lewishowes",        # Author and podcast host
        "rachelhollis",      # Author and speaker
        "deangraziosi",      # Real estate coach and author
    ]
    
    try:
        # Import the scraping functions
        from instagram_scraper import (
            get_instagram_profile,
            IndustryClassifier,
            meets_follower_criteria,
            has_recent_posts,
            is_us_based_profile,
            is_target_industry,
            extract_contact_info
        )
        
        # Initialize components
        print("🚀 Initializing components...")
        classifier = IndustryClassifier()
        print("   ✅ Industry classifier ready")
        
        # Prepare headers for Instagram API
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "x-ig-app-id": "936619743392459"
        }
        
        results = []
        qualified_leads = []
        
        print(f"\n🔍 Starting search for {len(test_usernames)} profiles...")
        print("-" * 60)
        
        for i, username in enumerate(test_usernames, 1):
            print(f"\n[{i}/{len(test_usernames)}] Analyzing @{username}...")
            
            try:
                # Add delay to be respectful to Instagram
                if i > 1:
                    delay = random.uniform(2, 4)
                    print(f"   ⏳ Waiting {delay:.1f}s...")
                    time.sleep(delay)
                
                # Get profile data
                print(f"   📡 Fetching profile data...")
                profile_data = get_instagram_profile(username, {}, headers)
                
                if not profile_data:
                    print(f"   ❌ Could not fetch data for @{username}")
                    results.append({
                        "username": username,
                        "status": "failed_to_fetch",
                        "reason": "API request failed"
                    })
                    continue
                
                # Extract basic info
                full_name = profile_data.get('full_name', 'N/A')
                followers = profile_data.get('edge_followed_by', {}).get('count', 0)
                posts = profile_data.get('edge_owner_to_timeline_media', {}).get('count', 0)
                bio = profile_data.get('biography', '')
                is_verified = profile_data.get('is_verified', False)
                
                print(f"   👤 {full_name}")
                print(f"   👥 {followers:,} followers")
                print(f"   📸 {posts} posts")
                print(f"   ✓ Verified: {is_verified}")
                print(f"   📝 Bio: {bio[:50]}..." if bio else "   📝 Bio: (empty)")
                
                # Apply filtering criteria
                print(f"   🔍 Applying filters...")
                
                follower_check = meets_follower_criteria(profile_data)
                posts_check = has_recent_posts(profile_data)
                us_check = is_us_based_profile(profile_data)
                industry_check = is_target_industry(bio, classifier) if bio else False
                
                print(f"      • Followers (100-10k): {'✅' if follower_check else '❌'}")
                print(f"      • Recent posts: {'✅' if posts_check else '❌'}")
                print(f"      • US-based: {'✅' if us_check else '❌'}")
                print(f"      • Target industry: {'✅' if industry_check else '❌'}")
                
                # Determine if qualified
                is_qualified = all([follower_check, posts_check, us_check, industry_check])
                
                if is_qualified:
                    print(f"   🎉 QUALIFIED LEAD!")
                    
                    # Extract contact info
                    contact_info = extract_contact_info(profile_data)
                    industry = classifier.predict(bio) if bio else "unknown"
                    
                    lead_data = {
                        "username": username,
                        "full_name": full_name,
                        "followers": followers,
                        "posts": posts,
                        "bio": bio,
                        "industry": industry,
                        "emails": contact_info.get('emails', []),
                        "phones": contact_info.get('phones', []),
                        "is_verified": is_verified,
                        "profile_url": f"https://instagram.com/{username}",
                        "status": "qualified"
                    }
                    
                    qualified_leads.append(lead_data)
                    
                    if contact_info.get('emails'):
                        print(f"   📧 Email found: {contact_info['emails'][0]}")
                    if contact_info.get('phones'):
                        print(f"   📱 Phone found: {contact_info['phones'][0]}")
                    
                else:
                    print(f"   ❌ FILTERED OUT")
                    
                    # Determine primary reason for filtering
                    if not follower_check:
                        reason = f"followers ({followers:,}) outside 100-10k range"
                    elif not posts_check:
                        reason = "no recent posts"
                    elif not us_check:
                        reason = "not US-based"
                    elif not industry_check:
                        reason = "not target industry"
                    else:
                        reason = "multiple criteria failed"
                    
                    results.append({
                        "username": username,
                        "full_name": full_name,
                        "followers": followers,
                        "status": "filtered_out",
                        "reason": reason
                    })
                
            except Exception as e:
                print(f"   ❌ Error processing @{username}: {e}")
                results.append({
                    "username": username,
                    "status": "error",
                    "reason": str(e)
                })
        
        # Show final results
        print("\n" + "=" * 60)
        print("📊 SEARCH RESULTS SUMMARY")
        print("=" * 60)
        
        print(f"Total profiles analyzed: {len(test_usernames)}")
        print(f"Qualified leads found: {len(qualified_leads)}")
        print(f"Filter success rate: {len(qualified_leads)/len(test_usernames)*100:.1f}%")
        
        if qualified_leads:
            print(f"\n🎯 QUALIFIED LEADS:")
            print("-" * 40)
            for lead in qualified_leads:
                print(f"• @{lead['username']} - {lead['full_name']}")
                print(f"  Industry: {lead['industry']}")
                print(f"  Followers: {lead['followers']:,}")
                if lead['emails']:
                    print(f"  Email: {lead['emails'][0]}")
                if lead['phones']:
                    print(f"  Phone: {lead['phones'][0]}")
                print(f"  Profile: {lead['profile_url']}")
                print()
        
        # Show filtered out profiles
        filtered_profiles = [r for r in results if r.get('status') == 'filtered_out']
        if filtered_profiles:
            print(f"❌ FILTERED OUT PROFILES:")
            print("-" * 40)
            for profile in filtered_profiles:
                print(f"• @{profile['username']} - {profile.get('full_name', 'N/A')}")
                print(f"  Reason: {profile['reason']}")
                print()
        
        # Save results to JSON
        all_results = {
            "search_date": datetime.now().isoformat(),
            "total_analyzed": len(test_usernames),
            "qualified_leads": qualified_leads,
            "filtered_profiles": filtered_profiles,
            "summary": {
                "qualified_count": len(qualified_leads),
                "filtered_count": len(filtered_profiles),
                "success_rate": len(qualified_leads)/len(test_usernames)*100
            }
        }
        
        with open('search_results.json', 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        print(f"📁 Detailed results saved to: search_results.json")
        
        return len(qualified_leads) > 0
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all dependencies are installed:")
        print("pip install requests pydantic scikit-learn")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Run the live search test."""
    print("🚀 Instagram Lead Generation - Live Search Test")
    print()
    print("This will test the scraper with real Instagram profiles")
    print("to show you what kind of leads it can find.")
    print()
    
    # Run the search
    success = run_live_search()
    
    if success:
        print("\n🎉 Search completed! Check the results above and in search_results.json")
    else:
        print("\n⚠️ Search had issues. Check the error messages above.")
    
    print("\n" + "=" * 60)
    print("Ready to run with your own target usernames! 🎯")

if __name__ == "__main__":
    main()
