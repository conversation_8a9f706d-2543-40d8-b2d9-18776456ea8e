#!/usr/bin/env python3
"""
Basic functionality test for Instagram scraper without external dependencies.
This tests the core components without requiring Google Sheets or proxy setup.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from instagram_scraper import (
    get_instagram_profile, 
    extract_contact_info, 
    IndustryClassifier, 
    InstagramProfile,
    attempt_email_verification
)
import logging
import unittest
from unittest.mock import patch, MagicMock
import requests # Required for requests.exceptions.RequestException
import random

# Assuming instagram_scraper.py is in the same directory or Python path
# These are already imported: get_instagram_profile, extract_contact_info, IndustryClassifier, InstagramProfile, attempt_email_verification
from instagram_scraper import search_instagram_by_keyword, USER_AGENTS, logger as instagram_logger # Add new imports

# Configure logging for this test file
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s')

# It's good practice to disable or lower the verbosity of the application's logger during tests
# unless specifically testing logging output.
instagram_logger.setLevel(logging.CRITICAL + 1)


# --- Existing Functional Tests ---

def test_industry_classifier():
    """Test the industry classification functionality."""
    print("\n=== Testing Industry Classifier ===")
    
    try:
        classifier = IndustryClassifier()
        
        test_bios = [
            "Life coach helping entrepreneurs achieve their goals",
            "Personal trainer specializing in weight loss",
            "Motivational speaker and author",
            "Business consultant for startups",
            "Content creator and influencer"
        ]
        
        for bio in test_bios:
            industry = classifier.predict(bio)
            print(f"Bio: '{bio}' -> Industry: {industry}")
            
        print("✅ Industry classifier working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Industry classifier failed: {e}")
        return False

def test_contact_extraction():
    """Test contact information extraction."""
    print("\n=== Testing Contact Extraction ===")
    
    try:
        # Mock profile data
        test_profile = {
            "biography": "Contact <NAME_EMAIL> or call ******-123-4567",
            "external_url": "https://mywebsite.com"
        }
        
        contact_info = extract_contact_info(test_profile)
        print(f"Extracted emails: {contact_info['emails']}")
        print(f"Extracted phones: {contact_info['phones']}")
        
        # Test with empty profile
        empty_contact = extract_contact_info({})
        print(f"Empty profile contact: {empty_contact}")
        
        print("✅ Contact extraction working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Contact extraction failed: {e}")
        return False

def test_pydantic_model():
    """Test the Pydantic model validation."""
    print("\n=== Testing Pydantic Model ===")
    
    try:
        # Test valid profile
        profile = InstagramProfile(
            username="testuser",
            full_name="Test User",
            first_name="Test",
            last_name="User",
            followers=1000,
            following=500,
            posts_count=50,
            verified_email="<EMAIL>",
            phone="******-123-4567",
            industry="coach",
            profile_url="https://instagram.com/testuser",
            is_verified=False
        )
        
        print(f"Created profile: {profile.username}")
        print(f"Full name: {profile.full_name}")
        print(f"Industry: {profile.industry}")
        print(f"Scraped at: {profile.scraped_at}")
        
        print("✅ Pydantic model working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Pydantic model failed: {e}")
        return False

def test_email_verification():
    """Test email verification placeholder."""
    print("\n=== Testing Email Verification ===")
    
    try:
        test_email = "<EMAIL>"
        verified_email = attempt_email_verification(test_email)
        print(f"Original: {test_email} -> Verified: {verified_email}")
        
        # Test with None
        verified_none = attempt_email_verification(None)
        print(f"None input -> Verified: {verified_none}")
        
        print("✅ Email verification placeholder working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Email verification failed: {e}")
        return False

def test_instagram_api_call():
    """Test Instagram API call (this might fail due to rate limits)."""
    print("\n=== Testing Instagram API Call ===")
    
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "x-ig-app-id": "***************"
        }
        
        # Test with a well-known public account
        username = "instagram"  # Official Instagram account
        profile_data = get_instagram_profile(username, {}, headers)
        
        if profile_data:
            print(f"Successfully fetched data for @{username}")
            print(f"Full name: {profile_data.get('full_name', 'N/A')}")
            print(f"Followers: {profile_data.get('edge_followed_by', {}).get('count', 'N/A')}")
            print(f"Is verified: {profile_data.get('is_verified', 'N/A')}")
            print("✅ Instagram API call working")
            return True
        else:
            print("⚠️ Instagram API call returned empty data (might be rate limited)")
            return False
            
    except Exception as e:
        print(f"⚠️ Instagram API call failed: {e}")
        print("This is expected if there are network issues or rate limits")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Instagram Scraper Basic Functionality Tests")
    print("=" * 60)
    
    tests = [
        test_industry_classifier,
        test_contact_extraction,
        test_pydantic_model,
        test_email_verification,
        test_instagram_api_call
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Functional Test Results Summary:")
    functional_passed = sum(results)
    functional_total = len(results)
    print(f"✅ Passed: {functional_passed}/{functional_total}")
    print(f"❌ Failed: {functional_total - functional_passed}/{functional_total}")
    
    functional_success = functional_passed >= functional_total -1 # Allow 1 failure for API call

    # --- Run unittest tests ---
    print("\n" + "=" * 60)
    print("🚀 Starting unittest-based tests for Search Functionality")
    print("=" * 60)
    # Discover and run tests from TestCase classes
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    # Add tests from TestSearchInstagramByKeyword class
    suite.addTests(loader.loadTestsFromTestCase(TestSearchInstagramByKeyword))

    runner = unittest.TextTestRunner(verbosity=2)
    unittest_results = runner.run(suite)

    unittest_success = unittest_results.wasSuccessful()

    print("\n" + "=" * 60)
    print("📊 Overall Test Results Summary:")
    if functional_success and unittest_success:
        print("\n🎉 All tests passed! Core functionality and search are working.")
        return True
    else:
        print(f"\n⚠️ Some tests failed. Functional tests success: {functional_success}. Unittest tests success: {unittest_success}")
        return False

# --- New Unittest Class for search_instagram_by_keyword ---

class TestSearchInstagramByKeyword(unittest.TestCase):

    def setUp(self):
        self.test_keyword = "testkeyword"
        self.test_proxy = {}
        # Ensure USER_AGENTS is available, if not, define a fallback for tests
        self.user_agents_list = USER_AGENTS if USER_AGENTS else ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"]
        self.test_headers = {
            "User-Agent": random.choice(self.user_agents_list),
            "x-ig-app-id": "***************"
        }

    @patch('instagram_scraper.requests.get')
    def test_search_success(self, mock_get):
        mock_response_data = {
            "users": [
                {"user": {"username": "testuser1"}},
                {"user": {"username": "testuser2"}},
                {"user": {"username": "testuser3"}}
            ],
            "status": "ok"
        }
        mock_response = MagicMock()
        mock_response.json.return_value = mock_response_data
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        expected_usernames = ["testuser1", "testuser2", "testuser3"]

        result = search_instagram_by_keyword(
            self.test_keyword,
            self.test_proxy,
            self.test_headers,
            max_results=10
        )

        expected_url = f"https://www.instagram.com/web/search/topsearch/?query={self.test_keyword}"
        mock_get.assert_called_once_with(
            expected_url,
            headers=self.test_headers,
            proxies=self.test_proxy,
            timeout=10
        )
        self.assertEqual(result, expected_usernames)
        mock_response.raise_for_status.assert_called_once()

    @patch('instagram_scraper.requests.get')
    def test_search_max_results(self, mock_get):
        mock_response_data = {
            "users": [
                {"user": {"username": "testuser1"}},
                {"user": {"username": "testuser2"}},
                {"user": {"username": "testuser3"}}
            ],
            "status": "ok"
        }
        mock_response = MagicMock()
        mock_response.json.return_value = mock_response_data
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        max_r = 1
        expected_usernames = ["testuser1"]

        result = search_instagram_by_keyword(
            self.test_keyword,
            self.test_proxy,
            self.test_headers,
            max_results=max_r
        )

        self.assertEqual(result, expected_usernames)
        self.assertEqual(len(result), max_r)

    @patch('instagram_scraper.requests.get')
    def test_search_api_error(self, mock_get):
        mock_get.side_effect = requests.exceptions.RequestException("Simulated API error")
        result = search_instagram_by_keyword(
            self.test_keyword,
            self.test_proxy,
            self.test_headers
        )
        self.assertEqual(result, [])

    @patch('instagram_scraper.requests.get')
    def test_search_malformed_json_missing_users_key(self, mock_get):
        mock_response_data = {"other_key": [], "status": "ok"}
        mock_response = MagicMock()
        mock_response.json.return_value = mock_response_data
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        result = search_instagram_by_keyword(
            self.test_keyword,
            self.test_proxy,
            self.test_headers
        )
        self.assertEqual(result, [])

    @patch('instagram_scraper.requests.get')
    def test_search_malformed_json_missing_user_subkey(self, mock_get):
        mock_response_data = {
            "users": [
                {"not_user_key": {"username": "testuser1"}},
                {"user": {"username": "testuser2"}}
            ],
            "status": "ok"
        }
        mock_response = MagicMock()
        mock_response.json.return_value = mock_response_data
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        expected_usernames = ["testuser2"]
        result = search_instagram_by_keyword(
            self.test_keyword,
            self.test_proxy,
            self.test_headers,
            max_results=5
        )
        self.assertEqual(result, expected_usernames)

    @patch('instagram_scraper.requests.get')
    def test_search_empty_users_list(self, mock_get):
        mock_response_data = {"users": [], "status": "ok"}
        mock_response = MagicMock()
        mock_response.json.return_value = mock_response_data
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        result = search_instagram_by_keyword(
            self.test_keyword,
            self.test_proxy,
            self.test_headers
        )
        self.assertEqual(result, [])

    @patch('instagram_scraper.requests.get')
    def test_search_http_error_after_success_call(self, mock_get):
        mock_response = MagicMock()
        mock_response.json.return_value = {"users": []}
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("Simulated HTTP error")
        mock_get.return_value = mock_response

        result = search_instagram_by_keyword(
            self.test_keyword,
            self.test_proxy,
            self.test_headers
        )
        self.assertEqual(result, [])
        mock_response.raise_for_status.assert_called_once()

if __name__ == "__main__":
    overall_success = main()
    sys.exit(0 if overall_success else 1)
