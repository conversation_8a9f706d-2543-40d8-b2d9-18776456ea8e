#!/usr/bin/env python3
"""
Basic functionality test for Instagram scraper without external dependencies.
This tests the core components without requiring Google Sheets or proxy setup.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from instagram_scraper import (
    get_instagram_profile, 
    extract_contact_info, 
    IndustryClassifier, 
    InstagramProfile,
    attempt_email_verification
)
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_industry_classifier():
    """Test the industry classification functionality."""
    print("\n=== Testing Industry Classifier ===")
    
    try:
        classifier = IndustryClassifier()
        
        test_bios = [
            "Life coach helping entrepreneurs achieve their goals",
            "Personal trainer specializing in weight loss",
            "Motivational speaker and author",
            "Business consultant for startups",
            "Content creator and influencer"
        ]
        
        for bio in test_bios:
            industry = classifier.predict(bio)
            print(f"Bio: '{bio}' -> Industry: {industry}")
            
        print("✅ Industry classifier working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Industry classifier failed: {e}")
        return False

def test_contact_extraction():
    """Test contact information extraction."""
    print("\n=== Testing Contact Extraction ===")
    
    try:
        # Mock profile data
        test_profile = {
            "biography": "Contact <NAME_EMAIL> or call ******-123-4567",
            "external_url": "https://mywebsite.com"
        }
        
        contact_info = extract_contact_info(test_profile)
        print(f"Extracted emails: {contact_info['emails']}")
        print(f"Extracted phones: {contact_info['phones']}")
        
        # Test with empty profile
        empty_contact = extract_contact_info({})
        print(f"Empty profile contact: {empty_contact}")
        
        print("✅ Contact extraction working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Contact extraction failed: {e}")
        return False

def test_pydantic_model():
    """Test the Pydantic model validation."""
    print("\n=== Testing Pydantic Model ===")
    
    try:
        # Test valid profile
        profile = InstagramProfile(
            username="testuser",
            full_name="Test User",
            first_name="Test",
            last_name="User",
            followers=1000,
            following=500,
            posts_count=50,
            verified_email="<EMAIL>",
            phone="******-123-4567",
            industry="coach",
            profile_url="https://instagram.com/testuser",
            is_verified=False
        )
        
        print(f"Created profile: {profile.username}")
        print(f"Full name: {profile.full_name}")
        print(f"Industry: {profile.industry}")
        print(f"Scraped at: {profile.scraped_at}")
        
        print("✅ Pydantic model working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Pydantic model failed: {e}")
        return False

def test_email_verification():
    """Test email verification placeholder."""
    print("\n=== Testing Email Verification ===")
    
    try:
        test_email = "<EMAIL>"
        verified_email = attempt_email_verification(test_email)
        print(f"Original: {test_email} -> Verified: {verified_email}")
        
        # Test with None
        verified_none = attempt_email_verification(None)
        print(f"None input -> Verified: {verified_none}")
        
        print("✅ Email verification placeholder working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Email verification failed: {e}")
        return False

def test_instagram_api_call():
    """Test Instagram API call (this might fail due to rate limits)."""
    print("\n=== Testing Instagram API Call ===")
    
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "x-ig-app-id": "***************"
        }
        
        # Test with a well-known public account
        username = "instagram"  # Official Instagram account
        profile_data = get_instagram_profile(username, {}, headers)
        
        if profile_data:
            print(f"Successfully fetched data for @{username}")
            print(f"Full name: {profile_data.get('full_name', 'N/A')}")
            print(f"Followers: {profile_data.get('edge_followed_by', {}).get('count', 'N/A')}")
            print(f"Is verified: {profile_data.get('is_verified', 'N/A')}")
            print("✅ Instagram API call working")
            return True
        else:
            print("⚠️ Instagram API call returned empty data (might be rate limited)")
            return False
            
    except Exception as e:
        print(f"⚠️ Instagram API call failed: {e}")
        print("This is expected if there are network issues or rate limits")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Instagram Scraper Basic Functionality Tests")
    print("=" * 60)
    
    tests = [
        test_industry_classifier,
        test_contact_extraction,
        test_pydantic_model,
        test_email_verification,
        test_instagram_api_call
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"✅ Passed: {sum(results)}/{len(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}/{len(results)}")
    
    if sum(results) >= len(results) - 1:  # Allow 1 failure (likely the API call)
        print("\n🎉 Core functionality is working! The scraper is ready for configuration.")
        return True
    else:
        print("\n⚠️ Some core components need attention before proceeding.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
