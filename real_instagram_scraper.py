#!/usr/bin/env python3
"""
Real Instagram Scraper using Instaloader
This script uses the Instaloader library to fetch actual Instagram data.
"""

import sys
import os
import json
import time
import csv
from datetime import datetime, timedelta
from typing import List, Dict, Optional

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    import instaloader
    INSTALOADER_AVAILABLE = True
    logger.info("Instaloader is available for real Instagram scraping")
except ImportError:
    INSTALOADER_AVAILABLE = False
    logger.warning("Instaloader not available. Install with: pip install instaloader")

# Import our filtering functions
try:
    from instagram_scraper import (
        IndustryClassifier,
        meets_follower_criteria,
        has_recent_posts,
        is_us_based_profile,
        is_target_industry,
        extract_contact_info
    )
    FILTERS_AVAILABLE = True
except ImportError:
    FILTERS_AVAILABLE = False
    logger.warning("Instagram scraper filters not available")

class RealInstagramScraper:
    """
    Real Instagram scraper using Instaloader library.
    """
    
    def __init__(self, username: Optional[str] = None, password: Optional[str] = None):
        """
        Initialize the Instagram scraper.
        
        Args:
            username: Instagram username for login (optional, but recommended for better access)
            password: Instagram password for login (optional)
        """
        if not INSTALOADER_AVAILABLE:
            raise ImportError("Instaloader not available. Install with: pip install instaloader")
        
        self.loader = instaloader.Instaloader(
            download_pictures=False,
            download_videos=False,
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=False,
            save_metadata=False,
            compress_json=False
        )
        
        # Login if credentials provided
        if username and password:
            try:
                self.loader.login(username, password)
                logger.info(f"Successfully logged in as {username}")
                self.logged_in = True
            except Exception as e:
                logger.warning(f"Login failed: {e}. Continuing without login (limited access)")
                self.logged_in = False
        else:
            logger.info("No login credentials provided. Using anonymous access (limited)")
            self.logged_in = False
        
        # Initialize industry classifier if available
        if FILTERS_AVAILABLE:
            self.industry_classifier = IndustryClassifier()
            logger.info("Industry classifier initialized")
        else:
            self.industry_classifier = None
            logger.warning("Industry classifier not available")
    
    def search_hashtag(self, hashtag: str, max_posts: int = 50) -> List[str]:
        """
        Search for posts by hashtag and extract usernames.
        
        Args:
            hashtag: Hashtag to search (without #)
            max_posts: Maximum number of posts to analyze
            
        Returns:
            List of unique usernames found
        """
        logger.info(f"🔍 Searching hashtag #{hashtag} for up to {max_posts} posts...")
        
        try:
            hashtag_obj = instaloader.Hashtag.from_name(self.loader.context, hashtag)
            usernames = set()
            
            post_count = 0
            for post in hashtag_obj.get_posts():
                if post_count >= max_posts:
                    break
                
                username = post.owner_username
                usernames.add(username)
                post_count += 1
                
                if post_count % 10 == 0:
                    logger.info(f"   Processed {post_count} posts, found {len(usernames)} unique users")
                
                # Rate limiting
                time.sleep(1)
            
            unique_usernames = list(usernames)
            logger.info(f"✅ Found {len(unique_usernames)} unique usernames from #{hashtag}")
            return unique_usernames
            
        except Exception as e:
            logger.error(f"Error searching hashtag #{hashtag}: {e}")
            return []
    
    def get_profile_data(self, username: str) -> Optional[Dict]:
        """
        Get detailed profile data for a username.
        
        Args:
            username: Instagram username (without @)
            
        Returns:
            Dictionary with profile data or None if error
        """
        try:
            logger.info(f"📡 Fetching profile data for @{username}...")
            
            profile = instaloader.Profile.from_username(self.loader.context, username)
            
            # Extract profile data
            profile_data = {
                'username': profile.username,
                'full_name': profile.full_name,
                'biography': profile.biography,
                'followers': profile.followers,
                'following': profile.followees,
                'posts_count': profile.mediacount,
                'is_verified': profile.is_verified,
                'is_private': profile.is_private,
                'external_url': profile.external_url,
                'profile_pic_url': profile.profile_pic_url,
                'is_business_account': profile.is_business_account,
                'business_category_name': getattr(profile, 'business_category_name', None),
                'scraped_at': datetime.now().isoformat()
            }
            
            logger.info(f"   👤 {profile_data['full_name']}")
            logger.info(f"   👥 {profile_data['followers']:,} followers")
            logger.info(f"   📸 {profile_data['posts_count']} posts")
            logger.info(f"   ✓ Verified: {profile_data['is_verified']}")
            
            return profile_data
            
        except instaloader.exceptions.ProfileNotExistsException:
            logger.warning(f"Profile @{username} does not exist")
            return None
        except instaloader.exceptions.LoginRequiredException:
            logger.warning(f"Login required to access @{username}")
            return None
        except Exception as e:
            logger.error(f"Error fetching profile @{username}: {e}")
            return None
    
    def check_profile_qualifies(self, profile_data: Dict) -> Dict:
        """
        Check if a profile meets our qualification criteria.
        
        Args:
            profile_data: Profile data dictionary
            
        Returns:
            Dictionary with qualification results
        """
        if not FILTERS_AVAILABLE or not self.industry_classifier:
            logger.warning("Filters not available, skipping qualification check")
            return {
                'qualifies': False,
                'reason': 'Filters not available',
                'checks': {}
            }
        
        username = profile_data['username']
        bio = profile_data.get('biography', '') or ''
        
        # Convert to format expected by our filters
        filter_format = {
            'edge_followed_by': {'count': profile_data['followers']},
            'edge_owner_to_timeline_media': {'count': profile_data['posts_count']},
            'biography': bio,
            'username': username,
            'full_name': profile_data.get('full_name', ''),
            'external_url': profile_data.get('external_url', ''),
            'is_verified': profile_data.get('is_verified', False)
        }
        
        # Apply filters
        follower_check = meets_follower_criteria(filter_format, max_followers=10000)
        posts_check = has_recent_posts(filter_format)
        us_check = is_us_based_profile(filter_format)
        industry_check = is_target_industry(bio, self.industry_classifier)
        
        # Check if private (we want public profiles)
        is_public = not profile_data.get('is_private', True)
        
        all_checks = {
            'followers': follower_check,
            'posts': posts_check,
            'us_based': us_check,
            'industry': industry_check,
            'is_public': is_public
        }
        
        qualifies = all(all_checks.values())
        
        logger.info(f"   🔍 Qualification check for @{username}:")
        logger.info(f"      • Followers (100-10k): {'✅' if follower_check else '❌'}")
        logger.info(f"      • Recent posts: {'✅' if posts_check else '❌'}")
        logger.info(f"      • US-based: {'✅' if us_check else '❌'}")
        logger.info(f"      • Target industry: {'✅' if industry_check else '❌'}")
        logger.info(f"      • Public profile: {'✅' if is_public else '❌'}")
        
        if qualifies:
            logger.info(f"   🎉 @{username} QUALIFIED!")
        else:
            failed_checks = [k for k, v in all_checks.items() if not v]
            logger.info(f"   ❌ @{username} filtered out (failed: {', '.join(failed_checks)})")
        
        return {
            'qualifies': qualifies,
            'checks': all_checks,
            'reason': 'Passed all checks' if qualifies else f"Failed: {', '.join([k for k, v in all_checks.items() if not v])}"
        }
    
    def discover_leads(self, hashtags: List[str], max_posts_per_hashtag: int = 20, max_qualified_leads: int = 50) -> List[Dict]:
        """
        Discover qualified leads from hashtag searches.
        
        Args:
            hashtags: List of hashtags to search
            max_posts_per_hashtag: Maximum posts to check per hashtag
            max_qualified_leads: Stop when this many qualified leads found
            
        Returns:
            List of qualified lead profiles
        """
        logger.info(f"🚀 Starting lead discovery from {len(hashtags)} hashtags")
        logger.info(f"Target: {max_qualified_leads} qualified leads")
        
        all_usernames = set()
        qualified_leads = []
        
        # Phase 1: Discover usernames from hashtags
        for hashtag in hashtags:
            if len(qualified_leads) >= max_qualified_leads:
                break
                
            usernames = self.search_hashtag(hashtag, max_posts_per_hashtag)
            all_usernames.update(usernames)
            
            logger.info(f"Total unique usernames discovered: {len(all_usernames)}")
            
            # Rate limiting between hashtag searches
            time.sleep(2)
        
        logger.info(f"📊 Discovery complete: {len(all_usernames)} unique usernames from {len(hashtags)} hashtags")
        
        # Phase 2: Check each profile for qualification
        logger.info(f"🎯 Phase 2: Qualifying profiles...")
        
        checked_count = 0
        for username in list(all_usernames):
            if len(qualified_leads) >= max_qualified_leads:
                break
            
            checked_count += 1
            logger.info(f"\n[{checked_count}/{len(all_usernames)}] Checking @{username}...")
            
            # Get profile data
            profile_data = self.get_profile_data(username)
            if not profile_data:
                continue
            
            # Check qualification
            qualification = self.check_profile_qualifies(profile_data)
            
            if qualification['qualifies']:
                # Extract contact info
                contact_info = {}
                if FILTERS_AVAILABLE:
                    bio = profile_data.get('biography', '')
                    external_url = profile_data.get('external_url', '')
                    contact_text = f"{bio} {external_url}"
                    contact_info = extract_contact_info(contact_text)
                
                # Add to qualified leads
                lead = {
                    **profile_data,
                    'contact_info': contact_info,
                    'qualification_checks': qualification['checks']
                }
                qualified_leads.append(lead)
                
                logger.info(f"✅ Qualified lead #{len(qualified_leads)}: @{username}")
                
                if len(qualified_leads) % 5 == 0:
                    logger.info(f"🎯 Progress: {len(qualified_leads)}/{max_qualified_leads} qualified leads found")
            
            # Rate limiting between profile checks
            time.sleep(3)
        
        logger.info(f"\n🎉 Lead discovery complete!")
        logger.info(f"📊 Results: {len(qualified_leads)} qualified leads from {checked_count} profiles checked")
        
        return qualified_leads
    
    def save_leads_to_csv(self, leads: List[Dict], filename: str = "real_instagram_leads.csv"):
        """
        Save qualified leads to CSV file.
        
        Args:
            leads: List of qualified lead dictionaries
            filename: Output CSV filename
        """
        if not leads:
            logger.warning("No leads to save")
            return
        
        headers = [
            'username', 'full_name', 'followers', 'following', 'posts_count',
            'biography', 'is_verified', 'is_business_account', 'external_url',
            'email', 'phone', 'website', 'scraped_at'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()
            
            for lead in leads:
                contact_info = lead.get('contact_info', {})
                
                row = {
                    'username': lead['username'],
                    'full_name': lead.get('full_name', ''),
                    'followers': lead['followers'],
                    'following': lead['following'],
                    'posts_count': lead['posts_count'],
                    'biography': lead.get('biography', ''),
                    'is_verified': lead.get('is_verified', False),
                    'is_business_account': lead.get('is_business_account', False),
                    'external_url': lead.get('external_url', ''),
                    'email': contact_info.get('emails', [''])[0] if contact_info.get('emails') else '',
                    'phone': contact_info.get('phones', [''])[0] if contact_info.get('phones') else '',
                    'website': contact_info.get('websites', [''])[0] if contact_info.get('websites') else '',
                    'scraped_at': lead['scraped_at']
                }
                writer.writerow(row)
        
        logger.info(f"💾 Saved {len(leads)} leads to {filename}")

def main():
    """Main function to run real Instagram lead discovery."""
    print("🚀 Real Instagram Lead Discovery using Instaloader")
    print("=" * 60)
    
    if not INSTALOADER_AVAILABLE:
        print("❌ Instaloader not installed!")
        print("Install with: pip install instaloader")
        return False
    
    if not FILTERS_AVAILABLE:
        print("❌ Instagram scraper filters not available!")
        print("Make sure instagram_scraper.py is in the same directory")
        return False
    
    # Initialize scraper (no login for demo - you can add credentials)
    scraper = RealInstagramScraper()
    
    # Define target hashtags
    target_hashtags = [
        'lifecoach',
        'businesscoach',
        'keynote',
        'motivationalspeaker',
        'author',
        'businessauthor',
        'consultant',
        'coursecreator'
    ]
    
    print(f"🎯 Target hashtags: {', '.join(['#' + h for h in target_hashtags])}")
    print(f"🔍 Looking for: Keynote Speakers, Coaches/Consultants/Course Creators, Authors")
    print(f"📊 Criteria: 100-10k followers, US-based, public profiles, recent activity")
    print()
    
    # Discover leads
    leads = scraper.discover_leads(
        hashtags=target_hashtags,
        max_posts_per_hashtag=10,  # Start small for testing
        max_qualified_leads=20     # Find 20 qualified leads
    )
    
    if leads:
        # Save to CSV
        scraper.save_leads_to_csv(leads, "real_instagram_leads.csv")
        
        # Show summary
        print(f"\n📊 FINAL RESULTS:")
        print(f"✅ Found {len(leads)} qualified Instagram leads")
        print(f"📁 Saved to: real_instagram_leads.csv")
        
        # Show sample leads
        print(f"\n🎯 SAMPLE QUALIFIED LEADS:")
        for i, lead in enumerate(leads[:5], 1):
            contact = lead.get('contact_info', {})
            email = contact.get('emails', [''])[0] if contact.get('emails') else 'No email'
            print(f"{i}. @{lead['username']} - {lead.get('full_name', 'No name')}")
            print(f"   Followers: {lead['followers']:,}")
            print(f"   Email: {email}")
            print(f"   Bio: {lead.get('biography', 'No bio')[:50]}...")
            print()
        
        return True
    else:
        print("❌ No qualified leads found. Try:")
        print("1. Different hashtags")
        print("2. Login with Instagram credentials")
        print("3. Adjust qualification criteria")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
