requests
pydantic
scikit-learn
openpyxl
pandas
instaloader

# Excel support for data storage
# openpyxl - for writing Excel files
# pandas - for data manipulation and Excel export

# Optional: Google Sheets support (commented out)
# gspread
# google-auth

# Placeholder for email verification service:
# If a specific email verification service (e.g., Apollo, Findymail, Hunter.io)
# is integrated in the future, its Python client library will need to be added here.
# Example:
# apollo-python==<version>
