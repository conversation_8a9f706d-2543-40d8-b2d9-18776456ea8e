#!/usr/bin/env python3
"""
Manual Google Search Guide for Instagram Username Discovery
This shows you exactly how to find real Instagram usernames using Google search.
"""

import sys
import os
import json
import re
from datetime import datetime
from typing import List, Dict

def show_google_search_queries():
    """Show the exact Google search queries to use."""
    print("🔍 GOOGLE SEARCH QUERIES FOR INSTAGRAM USERNAME DISCOVERY")
    print("=" * 70)
    print("Copy and paste these searches into Google to find real Instagram profiles:")
    print()
    
    queries = [
        {
            "category": "Life Coaches",
            "query": 'site:instagram.com "life coach" "entrepreneur" "coaching"',
            "description": "Find life coaches and entrepreneurship coaches"
        },
        {
            "category": "Business Coaches", 
            "query": 'site:instagram.com "business coach" "executive coach" "success coach"',
            "description": "Find business and executive coaches"
        },
        {
            "category": "Keynote Speakers",
            "query": 'site:instagram.com "keynote speaker" "motivational speaker" "public speaker"',
            "description": "Find professional speakers and motivational speakers"
        },
        {
            "category": "Authors",
            "query": 'site:instagram.com "author" "bestselling author" "published author"',
            "description": "Find published authors and writers"
        },
        {
            "category": "Business Consultants",
            "query": 'site:instagram.com "business consultant" "strategy consultant" "growth consultant"',
            "description": "Find business and strategy consultants"
        },
        {
            "category": "Course Creators",
            "query": 'site:instagram.com "course creator" "online course" "digital course"',
            "description": "Find online course creators and educators"
        },
        {
            "category": "US Business Professionals",
            "query": 'site:instagram.com "business" "entrepreneur" "USA" "United States"',
            "description": "Find US-based business professionals"
        },
        {
            "category": "Small Business Owners",
            "query": 'site:instagram.com "small business" "business owner" "entrepreneur" -influencer',
            "description": "Find small business owners (excluding influencers)"
        }
    ]
    
    for i, query_info in enumerate(queries, 1):
        print(f"{i}. {query_info['category']}")
        print(f"   Query: {query_info['query']}")
        print(f"   Purpose: {query_info['description']}")
        print()
    
    return queries

def show_manual_process():
    """Show the step-by-step manual process."""
    print("📋 STEP-BY-STEP MANUAL PROCESS")
    print("=" * 50)
    
    steps = [
        "1. Copy one of the Google search queries above",
        "2. Open Google in your web browser",
        "3. Paste the query and press Enter",
        "4. Look for results that show 'instagram.com/username'",
        "5. Extract the username from each Instagram URL",
        "6. Create a list of usernames to validate",
        "7. Use Instaloader to check if profiles exist and get data",
        "8. Apply your filtering criteria (followers, industry, etc.)"
    ]
    
    for step in steps:
        print(step)
    
    print(f"\n💡 EXAMPLE:")
    print("If you see: https://www.instagram.com/lifecoach_sarah/")
    print("Extract: lifecoach_sarah")
    print("Validate with: python -c \"import instaloader; l=instaloader.Instaloader(); p=instaloader.Profile.from_username(l.context, 'lifecoach_sarah'); print(f'{p.followers} followers')\"")

def show_url_extraction_examples():
    """Show examples of extracting usernames from Instagram URLs."""
    print("\n🔗 URL EXTRACTION EXAMPLES")
    print("=" * 40)
    
    examples = [
        {
            "url": "https://www.instagram.com/lifecoach_sarah/",
            "username": "lifecoach_sarah",
            "note": "Standard profile URL"
        },
        {
            "url": "https://instagram.com/businesscoach_mike",
            "username": "businesscoach_mike", 
            "note": "URL without trailing slash"
        },
        {
            "url": "https://www.instagram.com/speaker_john/?hl=en",
            "username": "speaker_john",
            "note": "URL with parameters"
        },
        {
            "url": "https://www.instagram.com/author_jane/p/ABC123/",
            "username": "author_jane",
            "note": "URL with post path (extract base username)"
        }
    ]
    
    print("Examples of extracting usernames from Instagram URLs:")
    print()
    
    for example in examples:
        print(f"URL: {example['url']}")
        print(f"Username: @{example['username']}")
        print(f"Note: {example['note']}")
        print()

def create_username_extraction_tool():
    """Create a simple tool to extract usernames from URLs."""
    print("🛠️ USERNAME EXTRACTION TOOL")
    print("=" * 40)
    
    tool_code = '''
import re
from urllib.parse import urlparse

def extract_instagram_username(url):
    """Extract username from Instagram URL."""
    try:
        # Parse URL
        parsed = urlparse(url)
        if 'instagram.com' not in parsed.netloc:
            return None
        
        # Extract path
        path = parsed.path.strip('/')
        
        # Skip certain paths
        skip_paths = ['explore', 'accounts', 'p', 'reel', 'tv', 'stories']
        if any(path.startswith(skip) for skip in skip_paths):
            return None
        
        # Extract username (first part of path)
        if '/' in path:
            username = path.split('/')[0]
        else:
            username = path
        
        # Validate username format
        if re.match(r'^[a-zA-Z0-9._]{1,30}$', username):
            return username
        
        return None
    except:
        return None

# Test examples
test_urls = [
    "https://www.instagram.com/lifecoach_sarah/",
    "https://instagram.com/businesscoach_mike",
    "https://www.instagram.com/speaker_john/?hl=en"
]

for url in test_urls:
    username = extract_instagram_username(url)
    print(f"{url} -> @{username}")
'''
    
    print("Save this code to extract usernames from URLs:")
    print()
    print(tool_code)

def show_validation_script():
    """Show how to validate discovered usernames."""
    print("\n✅ USERNAME VALIDATION SCRIPT")
    print("=" * 40)
    
    validation_code = '''
import instaloader
import time

def validate_instagram_usernames(usernames):
    """Validate list of Instagram usernames."""
    loader = instaloader.Instaloader(
        download_pictures=False,
        download_videos=False,
        save_metadata=False
    )
    
    valid_profiles = []
    
    for username in usernames:
        try:
            print(f"Checking @{username}...")
            profile = instaloader.Profile.from_username(loader.context, username)
            
            profile_data = {
                'username': profile.username,
                'full_name': profile.full_name,
                'followers': profile.followers,
                'biography': profile.biography,
                'is_verified': profile.is_verified,
                'is_private': profile.is_private,
                'external_url': profile.external_url
            }
            
            print(f"  ✅ Valid - {profile.followers:,} followers")
            valid_profiles.append(profile_data)
            
        except Exception as e:
            print(f"  ❌ Invalid - {e}")
        
        # Rate limiting
        time.sleep(2)
    
    return valid_profiles

# Example usage
discovered_usernames = [
    "lifecoach_sarah",
    "businesscoach_mike", 
    "speaker_john"
]

valid_profiles = validate_instagram_usernames(discovered_usernames)
print(f"\\nFound {len(valid_profiles)} valid profiles")
'''
    
    print("Use this script to validate discovered usernames:")
    print()
    print(validation_code)

def create_sample_discovery_results():
    """Create sample results showing what you might find."""
    print("\n📊 SAMPLE DISCOVERY RESULTS")
    print("=" * 40)
    print("Here's what you might find using Google searches:")
    print()
    
    sample_results = {
        "Life Coaches": [
            "@lifecoach_sarah - 2,450 followers - Life coach helping entrepreneurs",
            "@mindsetcoach_amy - 1,890 followers - Mindset and success coach",
            "@transformcoach_lisa - 3,200 followers - Transformation coach for women"
        ],
        "Business Coaches": [
            "@bizcoach_mike - 4,100 followers - Business coach for startups",
            "@executivecoach_tom - 2,800 followers - Executive leadership coach",
            "@scalecoach_jen - 1,950 followers - Helping businesses scale"
        ],
        "Keynote Speakers": [
            "@speaker_john - 3,500 followers - Professional keynote speaker",
            "@motivational_mary - 2,100 followers - Motivational speaker and author",
            "@corporate_speaker - 4,200 followers - Corporate training speaker"
        ],
        "Authors": [
            "@author_jane - 1,800 followers - Bestselling business author",
            "@writer_rob - 2,300 followers - Published author and writing coach",
            "@bookcoach_kim - 1,600 followers - Helping authors publish books"
        ]
    }
    
    total_found = 0
    for category, profiles in sample_results.items():
        print(f"🎯 {category}:")
        for profile in profiles:
            print(f"   {profile}")
            total_found += 1
        print()
    
    print(f"📊 TOTAL SAMPLE: {total_found} potential qualified leads")
    print("💡 These represent the types of profiles you can discover!")

def show_next_steps():
    """Show what to do after discovering usernames."""
    print("\n🚀 NEXT STEPS AFTER DISCOVERY")
    print("=" * 40)
    
    steps = [
        "1. 📝 Collect 20-50 usernames from Google searches",
        "2. ✅ Validate profiles using Instaloader",
        "3. 🔍 Apply your filtering criteria:",
        "   • Follower count (100-10k range)",
        "   • Industry classification",
        "   • US location detection", 
        "   • Contact information extraction",
        "4. 📊 Export qualified leads to Excel",
        "5. 🎯 Start outreach campaigns",
        "6. 📈 Scale up discovery for more leads"
    ]
    
    for step in steps:
        print(step)
    
    print(f"\n💡 INTEGRATION WITH YOUR SYSTEM:")
    print("Once you have real usernames, you can:")
    print("• Use them in your existing Instagram scraper")
    print("• Apply your industry classification ML model")
    print("• Generate Excel files with qualified leads")
    print("• Start your outreach campaigns immediately")

def main():
    """Main function to show the complete Google search guide."""
    print("🔍 COMPLETE GUIDE: Google Search for Instagram Username Discovery")
    print("=" * 70)
    print("This guide shows you how to find REAL Instagram usernames using Google")
    print()
    
    # Show search queries
    queries = show_google_search_queries()
    
    # Show manual process
    show_manual_process()
    
    # Show URL extraction examples
    show_url_extraction_examples()
    
    # Show extraction tool
    create_username_extraction_tool()
    
    # Show validation script
    show_validation_script()
    
    # Show sample results
    create_sample_discovery_results()
    
    # Show next steps
    show_next_steps()
    
    print("\n" + "=" * 70)
    print("🎯 SUMMARY: Manual Google Search for Instagram Discovery")
    print("=" * 70)
    print("✅ 8 targeted Google search queries provided")
    print("✅ Step-by-step manual process explained")
    print("✅ Username extraction tools provided")
    print("✅ Validation scripts included")
    print("✅ Integration with your existing system ready")
    
    print(f"\n🚀 START NOW:")
    print("1. Copy the first Google search query above")
    print("2. Paste it into Google and search")
    print("3. Extract usernames from Instagram URLs")
    print("4. Validate with Instaloader")
    print("5. Apply your filtering system")
    print("6. Generate qualified leads!")
    
    print(f"\n💡 This method will give you REAL Instagram usernames")
    print("that you can immediately use in your lead generation system!")

if __name__ == "__main__":
    main()
