#!/usr/bin/env python3
"""
Test Real Instagram Lead Generation
This script tests our complete system with real Instagram usernames to generate actual qualified leads.
"""

import sys
import os
import json
import time
import csv
from datetime import datetime
from typing import List, Dict, Optional

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    import instaloader
    INSTALOADER_AVAILABLE = True
except ImportError:
    INSTALOADER_AVAILABLE = False
    logger.warning("Instaloader not available. Install with: pip install instaloader")

# Import our filtering functions
try:
    from instagram_scraper import (
        IndustryClassifier,
        meets_follower_criteria,
        has_recent_posts,
        is_us_based_profile,
        is_target_industry,
        extract_contact_info
    )
    FILTERS_AVAILABLE = True
except ImportError:
    FILTERS_AVAILABLE = False
    logger.warning("Instagram scraper filters not available")

class RealLeadGenerator:
    """
    Real Instagram lead generator using actual usernames and Instaloader.
    """
    
    def __init__(self):
        """Initialize the lead generator."""
        if not INSTALOADER_AVAILABLE:
            raise ImportError("Instaloader not available. Install with: pip install instaloader")
        
        self.loader = instaloader.Instaloader(
            download_pictures=False,
            download_videos=False,
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=False,
            save_metadata=False,
            compress_json=False
        )
        
        # Initialize industry classifier if available
        if FILTERS_AVAILABLE:
            self.industry_classifier = IndustryClassifier()
            logger.info("✅ Industry classifier initialized")
        else:
            self.industry_classifier = None
            logger.warning("❌ Industry classifier not available")
    
    def get_test_usernames(self) -> List[str]:
        """
        Get a list of real Instagram usernames to test.
        These are actual usernames that likely exist and might match our criteria.
        """
        # Real Instagram usernames that are likely to exist and match our target criteria
        # These are common patterns for coaches, speakers, authors, and consultants
        test_usernames = [
            # Life coaches (common patterns)
            "lifecoach",
            "mindsetcoach", 
            "successcoach",
            "transformationcoach",
            
            # Business coaches
            "businesscoach",
            "executivecoach",
            "leadershipcoach",
            "entrepreneurcoach",
            
            # Speakers
            "motivationalspeaker",
            "keynote",
            "publicspeaker",
            "speaker",
            
            # Authors
            "author",
            "writer",
            "bookcoach",
            "publishedauthor",
            
            # Consultants
            "consultant",
            "businessconsultant",
            "strategyconsultant",
            "growthconsultant",
            
            # Course creators
            "coursecreator",
            "onlinecourse",
            "digitalcourse",
            "educator",
            
            # Generic business terms that might have good profiles
            "entrepreneur",
            "business",
            "coaching",
            "mentoring",
            "training"
        ]
        
        logger.info(f"📋 Testing with {len(test_usernames)} real Instagram usernames")
        return test_usernames
    
    def validate_profile(self, username: str) -> Optional[Dict]:
        """
        Validate an Instagram profile and get its data.
        
        Args:
            username: Instagram username to validate
            
        Returns:
            Profile data dictionary or None if invalid
        """
        try:
            logger.info(f"📡 Checking @{username}...")
            
            profile = instaloader.Profile.from_username(self.loader.context, username)
            
            # Extract profile data
            profile_data = {
                'username': profile.username,
                'full_name': profile.full_name,
                'biography': profile.biography,
                'followers': profile.followers,
                'following': profile.followees,
                'posts_count': profile.mediacount,
                'is_verified': profile.is_verified,
                'is_private': profile.is_private,
                'external_url': profile.external_url,
                'is_business_account': profile.is_business_account,
                'scraped_at': datetime.now().isoformat()
            }
            
            logger.info(f"   ✅ Profile exists!")
            logger.info(f"   👤 {profile_data['full_name'] or 'No name'}")
            logger.info(f"   👥 {profile_data['followers']:,} followers")
            logger.info(f"   📸 {profile_data['posts_count']} posts")
            logger.info(f"   🔒 Private: {profile_data['is_private']}")
            logger.info(f"   ✓ Verified: {profile_data['is_verified']}")
            
            return profile_data
            
        except instaloader.exceptions.ProfileNotExistsException:
            logger.info(f"   ❌ Profile @{username} does not exist")
            return None
        except instaloader.exceptions.LoginRequiredException:
            logger.info(f"   ⚠️ Login required to access @{username}")
            return None
        except Exception as e:
            logger.info(f"   ❌ Error: {e}")
            return None
    
    def qualify_profile(self, profile_data: Dict) -> Dict:
        """
        Apply qualification criteria to a profile.
        
        Args:
            profile_data: Profile data dictionary
            
        Returns:
            Qualification results
        """
        if not FILTERS_AVAILABLE or not self.industry_classifier:
            logger.warning("Filters not available, basic qualification only")
            
            # Basic qualification without ML
            basic_checks = {
                'followers': 100 <= profile_data['followers'] <= 10000,
                'posts': profile_data['posts_count'] > 10,
                'public': not profile_data['is_private'],
                'has_bio': bool(profile_data.get('biography', '').strip())
            }
            
            qualifies = all(basic_checks.values())
            
            return {
                'qualifies': qualifies,
                'checks': basic_checks,
                'reason': 'Basic qualification' if qualifies else 'Failed basic checks'
            }
        
        # Full qualification with ML
        username = profile_data['username']
        bio = profile_data.get('biography', '') or ''
        
        # Convert to format expected by our filters
        filter_format = {
            'edge_followed_by': {'count': profile_data['followers']},
            'edge_owner_to_timeline_media': {'count': profile_data['posts_count']},
            'biography': bio,
            'username': username,
            'full_name': profile_data.get('full_name', ''),
            'external_url': profile_data.get('external_url', ''),
            'is_verified': profile_data.get('is_verified', False)
        }
        
        # Apply all filters
        follower_check = meets_follower_criteria(filter_format, max_followers=10000)
        posts_check = has_recent_posts(filter_format)
        us_check = is_us_based_profile(filter_format)
        industry_check = is_target_industry(bio, self.industry_classifier)
        public_check = not profile_data.get('is_private', True)
        
        all_checks = {
            'followers': follower_check,
            'posts': posts_check,
            'us_based': us_check,
            'industry': industry_check,
            'public': public_check
        }
        
        qualifies = all(all_checks.values())
        
        logger.info(f"   🔍 Qualification check:")
        logger.info(f"      • Followers (100-10k): {'✅' if follower_check else '❌'}")
        logger.info(f"      • Recent posts: {'✅' if posts_check else '❌'}")
        logger.info(f"      • US-based: {'✅' if us_check else '❌'}")
        logger.info(f"      • Target industry: {'✅' if industry_check else '❌'}")
        logger.info(f"      • Public profile: {'✅' if public_check else '❌'}")
        
        if qualifies:
            logger.info(f"   🎉 @{username} QUALIFIED!")
        else:
            failed_checks = [k for k, v in all_checks.items() if not v]
            logger.info(f"   ❌ @{username} filtered out (failed: {', '.join(failed_checks)})")
        
        return {
            'qualifies': qualifies,
            'checks': all_checks,
            'reason': 'Passed all checks' if qualifies else f"Failed: {', '.join([k for k, v in all_checks.items() if not v])}"
        }
    
    def extract_contact_information(self, profile_data: Dict) -> Dict:
        """
        Extract contact information from profile.
        
        Args:
            profile_data: Profile data dictionary
            
        Returns:
            Contact information dictionary
        """
        if not FILTERS_AVAILABLE:
            return {}
        
        bio = profile_data.get('biography', '') or ''
        external_url = profile_data.get('external_url', '') or ''
        contact_text = f"{bio} {external_url}"
        
        return extract_contact_info(contact_text)
    
    def generate_real_leads(self, max_profiles: int = 20, max_qualified: int = 10) -> List[Dict]:
        """
        Generate real qualified leads from actual Instagram profiles.
        
        Args:
            max_profiles: Maximum profiles to check
            max_qualified: Stop when this many qualified leads found
            
        Returns:
            List of qualified lead profiles
        """
        logger.info("🚀 Starting Real Instagram Lead Generation")
        logger.info(f"🎯 Target: {max_qualified} qualified leads from {max_profiles} profiles")
        logger.info("🔍 Using real Instagram usernames and Instaloader validation")
        print()
        
        test_usernames = self.get_test_usernames()
        qualified_leads = []
        profiles_checked = 0
        
        for username in test_usernames:
            if profiles_checked >= max_profiles or len(qualified_leads) >= max_qualified:
                break
            
            profiles_checked += 1
            logger.info(f"\n[{profiles_checked}/{max_profiles}] Testing @{username}")
            
            # Validate profile
            profile_data = self.validate_profile(username)
            if not profile_data:
                continue
            
            # Qualify profile
            qualification = self.qualify_profile(profile_data)
            
            if qualification['qualifies']:
                # Extract contact info
                contact_info = self.extract_contact_information(profile_data)
                
                # Create qualified lead
                lead = {
                    **profile_data,
                    'contact_info': contact_info,
                    'qualification_checks': qualification['checks'],
                    'qualification_reason': qualification['reason']
                }
                
                qualified_leads.append(lead)
                logger.info(f"✅ Qualified lead #{len(qualified_leads)}: @{username}")
                
                if len(qualified_leads) % 3 == 0:
                    logger.info(f"🎯 Progress: {len(qualified_leads)}/{max_qualified} qualified leads found")
            
            # Rate limiting
            time.sleep(3)
        
        logger.info(f"\n🎉 Real lead generation complete!")
        logger.info(f"📊 Results: {len(qualified_leads)} qualified leads from {profiles_checked} profiles checked")
        logger.info(f"📈 Success rate: {len(qualified_leads)/profiles_checked*100:.1f}%")
        
        return qualified_leads
    
    def save_real_leads(self, leads: List[Dict], filename: str = "real_qualified_leads.csv"):
        """
        Save real qualified leads to CSV file.
        
        Args:
            leads: List of qualified lead dictionaries
            filename: Output CSV filename
        """
        if not leads:
            logger.warning("No leads to save")
            return
        
        headers = [
            'username', 'full_name', 'followers', 'following', 'posts_count',
            'biography', 'is_verified', 'is_business_account', 'external_url',
            'email', 'phone', 'website', 'qualification_reason', 'scraped_at'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()
            
            for lead in leads:
                contact_info = lead.get('contact_info', {})
                
                row = {
                    'username': lead['username'],
                    'full_name': lead.get('full_name', ''),
                    'followers': lead['followers'],
                    'following': lead['following'],
                    'posts_count': lead['posts_count'],
                    'biography': lead.get('biography', ''),
                    'is_verified': lead.get('is_verified', False),
                    'is_business_account': lead.get('is_business_account', False),
                    'external_url': lead.get('external_url', ''),
                    'email': contact_info.get('emails', [''])[0] if contact_info.get('emails') else '',
                    'phone': contact_info.get('phones', [''])[0] if contact_info.get('phones') else '',
                    'website': contact_info.get('websites', [''])[0] if contact_info.get('websites') else '',
                    'qualification_reason': lead.get('qualification_reason', ''),
                    'scraped_at': lead['scraped_at']
                }
                writer.writerow(row)
        
        logger.info(f"💾 Saved {len(leads)} real qualified leads to {filename}")

def main():
    """Main function to test real lead generation."""
    print("🚀 Testing Real Instagram Lead Generation")
    print("=" * 60)
    print("This test uses REAL Instagram usernames and Instaloader to generate actual qualified leads")
    print()
    
    if not INSTALOADER_AVAILABLE:
        print("❌ Instaloader not installed!")
        print("Install with: pip install instaloader")
        return False
    
    if not FILTERS_AVAILABLE:
        print("⚠️ Instagram scraper filters not available!")
        print("Will use basic qualification criteria only")
    
    # Initialize lead generator
    try:
        generator = RealLeadGenerator()
    except Exception as e:
        print(f"❌ Failed to initialize lead generator: {e}")
        return False
    
    print(f"🎯 Target: Keynote Speakers, Coaches/Consultants/Course Creators, Authors")
    print(f"🔍 Method: Real Instagram usernames + Instaloader validation")
    print(f"📊 Criteria: 100-10k followers, US-based, public profiles, target industries")
    print()
    
    # Generate real leads
    leads = generator.generate_real_leads(
        max_profiles=15,  # Check up to 15 profiles
        max_qualified=5   # Find 5 qualified leads
    )
    
    if leads:
        # Save to CSV
        generator.save_real_leads(leads, "real_qualified_leads.csv")
        
        # Show results
        print(f"\n📊 REAL LEAD GENERATION RESULTS:")
        print(f"✅ Found {len(leads)} qualified Instagram leads")
        print(f"📁 Saved to: real_qualified_leads.csv")
        
        # Show sample leads
        print(f"\n🎯 QUALIFIED REAL LEADS:")
        for i, lead in enumerate(leads, 1):
            contact = lead.get('contact_info', {})
            email = contact.get('emails', [''])[0] if contact.get('emails') else 'No email found'
            
            print(f"{i}. @{lead['username']} - {lead.get('full_name', 'No name')}")
            print(f"   Followers: {lead['followers']:,}")
            print(f"   Email: {email}")
            print(f"   Bio: {lead.get('biography', 'No bio')[:60]}...")
            print(f"   Reason: {lead.get('qualification_reason', 'Unknown')}")
            print()
        
        print(f"🎉 SUCCESS! Generated {len(leads)} real qualified leads!")
        print(f"💡 These are actual Instagram profiles that meet your criteria")
        print(f"📈 Ready for immediate outreach campaigns!")
        
        return True
    else:
        print("❌ No qualified leads found in this test")
        print("This could mean:")
        print("1. The test usernames don't match your criteria")
        print("2. Profiles are private or don't exist")
        print("3. Filtering criteria are too strict")
        print("4. Need to test with more usernames")
        
        print(f"\n💡 NEXT STEPS:")
        print("1. Try Google search to find more specific usernames")
        print("2. Adjust filtering criteria if needed")
        print("3. Test with usernames from your target industries")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
