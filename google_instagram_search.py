#!/usr/bin/env python3
"""
Google Search Automation for Instagram Username Discovery
This script automates Google searches to find real Instagram profiles.
"""

import sys
import os
import json
import time
import re
import csv
from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import quote_plus, urlparse

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    import requests
    from bs4 import BeautifulSoup
    SCRAPING_AVAILABLE = True
except ImportError:
    SCRAPING_AVAILABLE = False
    logger.warning("BeautifulSoup not available. Install with: pip install beautifulsoup4")

try:
    import instaloader
    INSTALOADER_AVAILABLE = True
except ImportError:
    INSTALOADER_AVAILABLE = False

class GoogleInstagramSearcher:
    """
    Automated Google search for Instagram profiles.
    """
    
    def __init__(self):
        """Initialize the searcher."""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Initialize Instaloader if available
        if INSTALOADER_AVAILABLE:
            self.loader = instaloader.Instaloader(
                download_pictures=False,
                download_videos=False,
                download_video_thumbnails=False,
                download_geotags=False,
                download_comments=False,
                save_metadata=False
            )
        else:
            self.loader = None
    
    def build_google_search_queries(self) -> List[Dict]:
        """
        Build Google search queries for finding Instagram profiles.
        
        Returns:
            List of search query dictionaries
        """
        queries = [
            {
                "name": "Life Coaches",
                "query": 'site:instagram.com "life coach" "entrepreneur" "coaching"',
                "keywords": ["life coach", "entrepreneur", "coaching", "mindset"]
            },
            {
                "name": "Business Coaches", 
                "query": 'site:instagram.com "business coach" "executive coach" "success coach"',
                "keywords": ["business coach", "executive", "success", "leadership"]
            },
            {
                "name": "Keynote Speakers",
                "query": 'site:instagram.com "keynote speaker" "motivational speaker" "public speaker"',
                "keywords": ["keynote", "speaker", "motivational", "conference"]
            },
            {
                "name": "Authors",
                "query": 'site:instagram.com "author" "bestselling author" "published author"',
                "keywords": ["author", "writer", "bestselling", "published"]
            },
            {
                "name": "Business Consultants",
                "query": 'site:instagram.com "business consultant" "strategy consultant" "growth consultant"',
                "keywords": ["consultant", "strategy", "growth", "advisor"]
            },
            {
                "name": "Course Creators",
                "query": 'site:instagram.com "course creator" "online course" "digital course"',
                "keywords": ["course", "online", "digital", "education"]
            },
            {
                "name": "US Business Professionals",
                "query": 'site:instagram.com "business" "entrepreneur" "USA" "United States"',
                "keywords": ["business", "entrepreneur", "USA", "professional"]
            },
            {
                "name": "Small Business Owners",
                "query": 'site:instagram.com "small business" "business owner" "entrepreneur" -influencer',
                "keywords": ["small business", "owner", "entrepreneur", "startup"]
            }
        ]
        
        return queries
    
    def search_google_for_instagram(self, query: str, max_results: int = 20) -> List[str]:
        """
        Search Google for Instagram profiles.
        
        Args:
            query: Google search query
            max_results: Maximum number of results to return
            
        Returns:
            List of Instagram usernames found
        """
        logger.info(f"🔍 Searching Google: {query}")
        
        try:
            # Build Google search URL
            search_url = f"https://www.google.com/search?q={quote_plus(query)}&num={max_results}"
            
            # Make request
            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            
            # Parse HTML
            if not SCRAPING_AVAILABLE:
                logger.warning("BeautifulSoup not available for parsing")
                return []
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find Instagram links
            instagram_links = []
            for link in soup.find_all('a', href=True):
                href = link['href']
                if 'instagram.com/' in href and '/url?q=' in href:
                    # Extract actual URL from Google redirect
                    match = re.search(r'/url\?q=([^&]+)', href)
                    if match:
                        actual_url = match.group(1)
                        if 'instagram.com/' in actual_url:
                            instagram_links.append(actual_url)
                elif 'instagram.com/' in href and href.startswith('http'):
                    instagram_links.append(href)
            
            # Extract usernames from Instagram URLs
            usernames = []
            for url in instagram_links:
                username = self.extract_username_from_url(url)
                if username and username not in usernames:
                    usernames.append(username)
            
            logger.info(f"   Found {len(usernames)} Instagram usernames")
            return usernames[:max_results]
            
        except Exception as e:
            logger.error(f"Error searching Google: {e}")
            return []
    
    def extract_username_from_url(self, url: str) -> Optional[str]:
        """
        Extract Instagram username from URL.
        
        Args:
            url: Instagram URL
            
        Returns:
            Username or None if invalid
        """
        try:
            # Parse URL
            parsed = urlparse(url)
            if 'instagram.com' not in parsed.netloc:
                return None
            
            # Extract path
            path = parsed.path.strip('/')
            
            # Skip certain paths
            skip_paths = ['explore', 'accounts', 'p', 'reel', 'tv', 'stories', 'direct']
            if any(path.startswith(skip) for skip in skip_paths):
                return None
            
            # Extract username (first part of path)
            if '/' in path:
                username = path.split('/')[0]
            else:
                username = path
            
            # Validate username format
            if re.match(r'^[a-zA-Z0-9._]{1,30}$', username) and len(username) > 0:
                return username
            
            return None
            
        except Exception:
            return None
    
    def validate_instagram_profile(self, username: str) -> Optional[Dict]:
        """
        Validate Instagram profile using Instaloader.
        
        Args:
            username: Instagram username
            
        Returns:
            Profile data or None if invalid
        """
        if not self.loader:
            logger.warning("Instaloader not available for validation")
            return None
        
        try:
            logger.info(f"   📡 Validating @{username}...")
            
            profile = instaloader.Profile.from_username(self.loader.context, username)
            
            profile_data = {
                'username': profile.username,
                'full_name': profile.full_name,
                'biography': profile.biography,
                'followers': profile.followers,
                'following': profile.followees,
                'posts_count': profile.mediacount,
                'is_verified': profile.is_verified,
                'is_private': profile.is_private,
                'external_url': profile.external_url,
                'is_business_account': profile.is_business_account,
                'scraped_at': datetime.now().isoformat()
            }
            
            logger.info(f"      ✅ Valid - {profile_data['followers']:,} followers")
            return profile_data
            
        except instaloader.exceptions.ProfileNotExistsException:
            logger.info(f"      ❌ Profile @{username} does not exist")
            return None
        except Exception as e:
            logger.info(f"      ⚠️ Error validating @{username}: {e}")
            return None
    
    def discover_usernames(self, max_per_query: int = 10, validate_profiles: bool = True) -> Dict:
        """
        Discover Instagram usernames using Google search.
        
        Args:
            max_per_query: Maximum results per search query
            validate_profiles: Whether to validate profiles with Instaloader
            
        Returns:
            Dictionary with discovery results
        """
        logger.info("🚀 Starting Google Instagram Username Discovery")
        
        queries = self.build_google_search_queries()
        all_usernames = set()
        validated_profiles = []
        
        for i, query_info in enumerate(queries, 1):
            logger.info(f"\n[{i}/{len(queries)}] {query_info['name']}")
            
            # Search Google
            usernames = self.search_google_for_instagram(
                query_info['query'], 
                max_results=max_per_query
            )
            
            if usernames:
                logger.info(f"   📋 Found usernames: {', '.join(['@' + u for u in usernames[:5]])}{'...' if len(usernames) > 5 else ''}")
                all_usernames.update(usernames)
                
                # Validate profiles if requested
                if validate_profiles and self.loader:
                    for username in usernames[:3]:  # Validate first 3 to avoid rate limits
                        profile_data = self.validate_instagram_profile(username)
                        if profile_data:
                            profile_data['discovery_query'] = query_info['name']
                            profile_data['discovery_keywords'] = query_info['keywords']
                            validated_profiles.append(profile_data)
                        
                        # Rate limiting
                        time.sleep(2)
            else:
                logger.info(f"   ❌ No usernames found")
            
            # Rate limiting between searches
            time.sleep(3)
        
        results = {
            'total_unique_usernames': len(all_usernames),
            'all_usernames': list(all_usernames),
            'validated_profiles': validated_profiles,
            'discovery_date': datetime.now().isoformat(),
            'queries_used': [q['name'] for q in queries]
        }
        
        logger.info(f"\n📊 DISCOVERY COMPLETE:")
        logger.info(f"   Total unique usernames: {len(all_usernames)}")
        logger.info(f"   Validated profiles: {len(validated_profiles)}")
        
        return results
    
    def save_results(self, results: Dict, filename_prefix: str = "google_instagram_discovery"):
        """
        Save discovery results to files.
        
        Args:
            results: Discovery results dictionary
            filename_prefix: Prefix for output files
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save all usernames to text file
        usernames_file = f"{filename_prefix}_usernames_{timestamp}.txt"
        with open(usernames_file, 'w') as f:
            for username in results['all_usernames']:
                f.write(f"@{username}\n")
        
        logger.info(f"💾 Saved {len(results['all_usernames'])} usernames to: {usernames_file}")
        
        # Save validated profiles to CSV
        if results['validated_profiles']:
            csv_file = f"{filename_prefix}_profiles_{timestamp}.csv"
            
            headers = [
                'username', 'full_name', 'followers', 'following', 'posts_count',
                'biography', 'is_verified', 'is_private', 'external_url',
                'discovery_query', 'scraped_at'
            ]
            
            with open(csv_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=headers)
                writer.writeheader()
                
                for profile in results['validated_profiles']:
                    row = {header: profile.get(header, '') for header in headers}
                    writer.writerow(row)
            
            logger.info(f"💾 Saved {len(results['validated_profiles'])} validated profiles to: {csv_file}")
        
        # Save complete results to JSON
        json_file = f"{filename_prefix}_complete_{timestamp}.json"
        with open(json_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"💾 Saved complete results to: {json_file}")
        
        return {
            'usernames_file': usernames_file,
            'csv_file': csv_file if results['validated_profiles'] else None,
            'json_file': json_file
        }

def show_manual_google_search_guide():
    """Show how to do manual Google searches."""
    print("\n📋 MANUAL GOOGLE SEARCH GUIDE")
    print("=" * 50)
    print("If automated search doesn't work, use these manual Google searches:")
    print()
    
    searches = [
        'site:instagram.com "life coach" "entrepreneur"',
        'site:instagram.com "business coach" "executive coach"',
        'site:instagram.com "keynote speaker" "motivational speaker"',
        'site:instagram.com "author" "bestselling author"',
        'site:instagram.com "business consultant" "strategy"',
        'site:instagram.com "course creator" "online course"'
    ]
    
    for i, search in enumerate(searches, 1):
        print(f"{i}. {search}")
    
    print(f"\n💡 MANUAL PROCESS:")
    print("1. Copy each search query above")
    print("2. Paste into Google search")
    print("3. Look for instagram.com/username links")
    print("4. Extract usernames from the URLs")
    print("5. Validate with Instaloader")

def main():
    """Main function to run Google Instagram discovery."""
    print("🔍 Google Instagram Username Discovery")
    print("=" * 50)
    
    if not SCRAPING_AVAILABLE:
        print("⚠️ BeautifulSoup not available for automated parsing")
        print("Install with: pip install beautifulsoup4")
        print("Showing manual search guide instead...")
        show_manual_google_search_guide()
        return False
    
    if not INSTALOADER_AVAILABLE:
        print("⚠️ Instaloader not available for profile validation")
        print("Install with: pip install instaloader")
        print("Will discover usernames without validation...")
    
    # Initialize searcher
    searcher = GoogleInstagramSearcher()
    
    print(f"🎯 Target: Keynote Speakers, Coaches/Consultants/Course Creators, Authors")
    print(f"🔍 Method: Automated Google search + Instagram validation")
    print()
    
    # Discover usernames
    results = searcher.discover_usernames(
        max_per_query=10,
        validate_profiles=INSTALOADER_AVAILABLE
    )
    
    # Save results
    files = searcher.save_results(results)
    
    # Show summary
    print(f"\n🎉 DISCOVERY COMPLETE!")
    print(f"📊 Results:")
    print(f"   • Total usernames found: {results['total_unique_usernames']}")
    print(f"   • Validated profiles: {len(results['validated_profiles'])}")
    print(f"   • Files created: {len([f for f in files.values() if f])}")
    
    if results['validated_profiles']:
        print(f"\n🎯 SAMPLE DISCOVERED PROFILES:")
        for i, profile in enumerate(results['validated_profiles'][:5], 1):
            print(f"{i}. @{profile['username']} - {profile.get('full_name', 'No name')}")
            print(f"   Followers: {profile['followers']:,}")
            print(f"   Query: {profile['discovery_query']}")
            print(f"   Bio: {profile.get('biography', 'No bio')[:50]}...")
            print()
    
    if results['total_unique_usernames'] > 0:
        print(f"💡 NEXT STEPS:")
        print("1. Review the discovered usernames")
        print("2. Validate more profiles with Instaloader")
        print("3. Apply your filtering criteria")
        print("4. Generate qualified leads")
        return True
    else:
        print("❌ No usernames discovered")
        print("Try manual Google searches or check internet connection")
        show_manual_google_search_guide()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
